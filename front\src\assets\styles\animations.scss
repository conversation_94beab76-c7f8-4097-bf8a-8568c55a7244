// 动画样式文件
// 为首页组件提供统一的动画效果

// ===== 基础动画关键帧 =====

// 淡入向上动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 淡入向下动画
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 淡入向左动画
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 淡入向右动画
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 缩放淡入动画
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 数字滚动动画
@keyframes numberRoll {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// 脉冲动画
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 摇摆动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

// 弹跳动画
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

// ===== 动画类名 =====

// 基础进入动画
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.fade-in-scale {
  animation: fadeInScale 0.6s ease-out;
}

// 延迟动画
.fade-in-up-delay-1 {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.fade-in-up-delay-2 {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.fade-in-up-delay-3 {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.fade-in-up-delay-4 {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

// 数字动画
.number-roll {
  animation: numberRoll 0.8s ease-out;
}

// 交互动画
.pulse {
  animation: pulse 2s infinite;
}

.shake {
  animation: shake 0.5s ease-in-out;
}

.bounce {
  animation: bounce 1s ease-in-out;
}

// ===== 悬停效果 =====

// 卡片悬停效果
.hover-lift {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

// 按钮悬停效果
.hover-scale {
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// 图片悬停效果
.hover-zoom {
  overflow: hidden;
  
  img {
    transition: transform 0.3s ease;
  }
  
  &:hover img {
    transform: scale(1.1);
  }
}

// 文字悬停效果
.hover-underline {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--brand-primary);
    transition: width 0.3s ease;
  }
  
  &:hover::after {
    width: 100%;
  }
}

// ===== 加载动画 =====

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

// 旋转加载动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

// ===== 滚动动画 =====

// 滚动触发的动画
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
  
  &.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

// 视差滚动效果
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

// ===== 响应式动画控制 =====

// 在移动设备上减少动画
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 移动端优化
@media (max-width: 768px) {
  .fade-in-up,
  .fade-in-down,
  .fade-in-left,
  .fade-in-right,
  .fade-in-scale {
    animation-duration: 0.4s;
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
  }
}

// ===== 性能优化 =====

// 硬件加速
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

// 防止动画期间的重绘
.no-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// ===== 特殊效果 =====

// 渐变背景动画
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animation {
  background: linear-gradient(-45deg, var(--brand-primary), var(--brand-secondary), var(--brand-primary-light), var(--brand-secondary-light));
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

// 打字机效果
@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--brand-primary);
  white-space: nowrap;
  animation: typewriter 2s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: var(--brand-primary);
  }
}

// ===== 工具类 =====

// 动画延迟工具类
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

// 动画持续时间工具类
.duration-fast { animation-duration: 0.3s; }
.duration-normal { animation-duration: 0.6s; }
.duration-slow { animation-duration: 1s; }

// 缓动函数工具类
.ease-in { animation-timing-function: ease-in; }
.ease-out { animation-timing-function: ease-out; }
.ease-in-out { animation-timing-function: ease-in-out; }
