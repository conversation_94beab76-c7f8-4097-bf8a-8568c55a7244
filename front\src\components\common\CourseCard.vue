<template>
  <div :class="['course-card', `course-card--${size}`, 'hover-lift']" @click="handleClick">
    <!-- 推荐理由标签 -->
    <div v-if="showRecommendationReason && recommendationText" class="recommendation-badge">
      {{ recommendationText }}
    </div>

    <!-- 课程封面图片 -->
    <div class="course-image hover-zoom">
      <img :src="course.image" :alt="course.title" />
      <!-- 课程标签 -->
      <div class="course-badges">
        <span v-for="badge in courseBadges" :key="badge.text" :class="['badge', badge.class]">
          {{ badge.text }}
        </span>
      </div>
    </div>

    <!-- 课程内容信息 -->
    <div class="card-content">
      <h4 class="course-title">{{ course.title }}</h4>
      <p class="instructor-name">{{ course.instructor.name }}</p>

      <!-- 课程评分和统计 -->
      <div class="course-stats">
        <div class="rating">
          <el-rate
            v-model="course.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
            size="small"
          />
          <span class="review-count">({{ course.reviewCount }})</span>
        </div>
      </div>

      <!-- 卡片底部信息 -->
      <div class="card-footer">
        <div class="price-info">
          <span class="price">¥{{ course.price }}</span>
          <span
            v-if="course.originalPrice && course.originalPrice > course.price"
            class="original-price"
          >
            ¥{{ course.originalPrice }}
          </span>
        </div>
        <span class="students">{{ course.studentsCount }}人学习</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElRate } from 'element-plus'
import type { Course } from '@/types/course'

// Props定义
interface Props {
  course: Course
  showRecommendationReason?: boolean
  recommendationText?: string
  size?: 'normal' | 'compact'
}

const props = withDefaults(defineProps<Props>(), {
  showRecommendationReason: false,
  recommendationText: '',
  size: 'normal',
})

// Events定义
const emit = defineEmits<{
  click: [courseId: string]
}>()

// 处理点击事件
const handleClick = () => {
  emit('click', props.course.id)
}

// 计算课程标签，避免重复显示
const courseBadges = computed(() => {
  const badges: Array<{ text: string; class: string }> = []

  // 如果有自定义badge，优先显示
  if (props.course.badge) {
    badges.push({
      text: props.course.badge,
      class: 'badge--special',
    })
  }

  // 如果没有自定义badge或自定义badge不是"热门"，且课程是热门，则显示热门标签
  if (props.course.isHot && props.course.badge !== '热门') {
    badges.push({
      text: '热门',
      class: 'badge--hot',
    })
  }

  // 如果没有自定义badge或自定义badge不是"新课"，且课程是新课，则显示新课标签
  if (props.course.isNew && props.course.badge !== '新课') {
    badges.push({
      text: '新课',
      class: 'badge--new',
    })
  }

  return badges
})
</script>

<style lang="scss" scoped>
.course-card {
  position: relative;
  background: var(--bg-primary);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  // 推荐理由标签
  .recommendation-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-primary-light));
    color: var(--text-inverse);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    z-index: 2;
    max-width: calc(100% - 24px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 课程图片区域
  .course-image {
    position: relative;

    img {
      width: 100%;
      height: 160px;
      object-fit: cover;
      display: block;
    }

    .course-badges {
      position: absolute;
      top: 12px;
      right: 12px;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .badge {
        padding: 2px 6px;
        border-radius: 8px;
        font-size: 10px;
        font-weight: 500;
        text-align: center;

        &--hot {
          background: var(--color-error);
          color: var(--text-inverse);
        }

        &--new {
          background: var(--color-success);
          color: var(--text-inverse);
        }

        &--special {
          background: var(--brand-accent);
          color: var(--text-inverse);
        }
      }
    }
  }

  // 卡片内容区域
  .card-content {
    padding: 20px;

    .course-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      color: var(--text-primary);
      font-weight: 600;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .instructor-name {
      margin: 0 0 12px 0;
      color: var(--text-secondary);
      font-size: 14px;
    }

    .course-stats {
      margin-bottom: 16px;

      .rating {
        display: flex;
        align-items: center;
        gap: 8px;

        .review-count {
          color: var(--text-tertiary);
          font-size: 12px;
        }
      }
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .price {
          color: var(--brand-primary);
          font-size: 18px;
          font-weight: bold;
        }

        .original-price {
          color: var(--text-tertiary);
          font-size: 14px;
          text-decoration: line-through;
        }
      }

      .students {
        color: var(--text-tertiary);
        font-size: 12px;
      }
    }
  }

  // 紧凑尺寸变体
  &--compact {
    .course-image img {
      height: 120px;
    }

    .card-content {
      padding: 16px;

      .course-title {
        font-size: 14px;
        -webkit-line-clamp: 1;
      }

      .instructor-name {
        font-size: 12px;
        margin-bottom: 8px;
      }

      .course-stats {
        margin-bottom: 12px;
      }

      .card-footer {
        .price-info .price {
          font-size: 16px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .course-card {
    .course-image img {
      height: 140px;
    }

    .card-content {
      padding: 16px;

      .course-title {
        font-size: 15px;
      }

      .card-footer {
        .price-info .price {
          font-size: 16px;
        }
      }
    }
  }
}
</style>
