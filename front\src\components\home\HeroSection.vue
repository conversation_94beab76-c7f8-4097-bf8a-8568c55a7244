<template>
  <section class="hero-section">
    <div class="container">
      <div class="hero-layout">
        <!-- 左侧边栏插槽 -->
        <aside ref="leftSidebar" class="left-sidebar scroll-reveal">
          <slot name="left-sidebar">
            <!-- 默认内容：如果没有提供插槽内容，显示默认提示 -->
            <div class="default-content">
              <p>左侧边栏内容</p>
            </div>
          </slot>
        </aside>

        <!-- 中间内容插槽 -->
        <div ref="centerContent" class="center-content scroll-reveal">
          <slot name="center-content">
            <!-- 默认内容：如果没有提供插槽内容，显示默认提示 -->
            <div class="default-content">
              <p>中间内容区域</p>
            </div>
          </slot>
        </div>

        <!-- 右侧边栏插槽 -->
        <aside ref="rightSidebar" class="right-sidebar scroll-reveal">
          <slot name="right-sidebar">
            <!-- 默认内容：如果没有提供插槽内容，显示默认提示 -->
            <div class="default-content">
              <p>右侧边栏内容</p>
            </div>
          </slot>
        </aside>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useScrollAnimation } from '@/composables/useIntersectionObserver'

// 元素引用
const leftSidebar = ref<HTMLElement>()
const centerContent = ref<HTMLElement>()
const rightSidebar = ref<HTMLElement>()

// 组件挂载后确保所有元素可见
onMounted(() => {
  // Hero区域应该立即可见，不需要滚动动画
  // 确保所有元素都是可见状态
  if (leftSidebar.value) {
    leftSidebar.value.style.opacity = '1'
    leftSidebar.value.style.transform = 'translateX(0)'
  }

  if (centerContent.value) {
    centerContent.value.style.opacity = '1'
    centerContent.value.style.transform = 'translateY(0)'
  }

  if (rightSidebar.value) {
    rightSidebar.value.style.opacity = '1'
    rightSidebar.value.style.transform = 'translateX(0)'
  }
})
</script>

<style lang="scss" scoped>
.hero-section {
  background: var(--background-light);

  .container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
  }

  .hero-layout {
    display: grid;
    grid-template-columns: 280px 1fr 320px; /* 压缩两侧边栏，为宽屏轮播图提供更多展示空间 */
    gap: 24px;
    padding: 24px;
    min-height: 200px; /* 减少最小高度以适应视口 */
    align-items: start;
  }

  .left-sidebar,
  .right-sidebar {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    height: fit-content;
    /* 初始状态为可见，避免动画导致的透明问题 */
    opacity: 1;
    transform: translateX(0);
  }

  .center-content {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    min-height: 180px; /* 减少最小高度 */
    /* 初始状态为可见，避免动画导致的透明问题 */
    opacity: 1;
    transform: translateY(0);
  }

  .default-content {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

// 响应式设计 - 针对紧凑型轮播图布局优化
@media (max-width: 1800px) {
  .hero-section {
    .hero-layout {
      grid-template-columns: 260px 1fr 300px; /* 大屏幕保持紧凑比例 */
      gap: 20px;
      padding: 20px;
      min-height: 180px; /* 减少最小高度 */
    }
  }
}

@media (max-width: 1600px) {
  .hero-section {
    .hero-layout {
      grid-template-columns: 240px 1fr 280px; /* 中等屏幕进一步压缩 */
      gap: 18px;
      padding: 18px;
      min-height: 160px; /* 减少最小高度 */
    }
  }
}

@media (max-width: 1400px) {
  .hero-section {
    .hero-layout {
      grid-template-columns: 220px 1fr 260px; /* 较小屏幕维持紧凑布局 */
      gap: 16px;
      padding: 16px;
      min-height: 140px; /* 减少最小高度 */
    }
  }
}

@media (max-width: 1024px) {
  .hero-section {
    .hero-layout {
      display: block;
      padding: 16px;
    }

    .left-sidebar,
    .right-sidebar {
      display: none; /* 在平板和手机上隐藏侧边栏 */
    }

    .center-content {
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 768px) {
  .hero-section {
    .hero-layout {
      padding: 12px;
    }

    .center-content {
      min-height: 200px;
    }
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .hero-section {
    .hero-layout {
      padding: 8px;
    }

    .center-content {
      min-height: 180px;
    }
  }
}
</style>
