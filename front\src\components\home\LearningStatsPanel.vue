<template>
  <div class="learning-stats-panel">
    <!-- 学习统计区域 -->
    <div class="stats-section">
      <h3 class="section-title">
        <el-icon class="title-icon"><TrendCharts /></el-icon>
        学习统计
      </h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number" :class="{ animate: animateNumbers }">
            {{ animatedTotalCourses }}
          </div>
          <div class="stat-label">精品课程</div>
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-number" :class="{ animate: animateNumbers }">
            {{ animatedTotalStudents }}
          </div>
          <div class="stat-label">学员总数</div>
          <div class="stat-icon">
            <el-icon><User /></el-icon>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-number" :class="{ animate: animateNumbers }">
            {{ animatedTotalHours }}
          </div>
          <div class="stat-label">学习时长</div>
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时动态区域 -->
    <div class="realtime-section">
      <h3 class="section-title">
        <el-icon class="title-icon"><Lightning /></el-icon>
        实时动态
      </h3>
      <div class="realtime-stats">
        <div class="realtime-item">
          <div class="realtime-indicator online"></div>
          <div class="realtime-content">
            <div class="realtime-number">{{ onlineUsers }}</div>
            <div class="realtime-label">当前在线</div>
          </div>
        </div>
        <div class="realtime-item">
          <div class="realtime-indicator new"></div>
          <div class="realtime-content">
            <div class="realtime-number">+{{ todayNewUsers }}</div>
            <div class="realtime-label">今日新增</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门趋势区域 -->
    <div class="trending-section">
      <h3 class="section-title">
        <el-icon class="title-icon"><TrendCharts /></el-icon>
        热门趋势
      </h3>
      <div class="trending-list">
        <div
          v-for="(trend, index) in trendingCourses"
          :key="trend.id"
          class="trending-item"
          :class="{ 'top-trend': index === 0 }"
        >
          <div class="trend-rank">{{ index + 1 }}</div>
          <div class="trend-content">
            <div class="trend-title">{{ trend.title }}</div>
            <div class="trend-stats">
              <span class="trend-students">{{ trend.studentsCount }}人学习</span>
              <span class="trend-growth" :class="trend.growthClass">
                <el-icon><ArrowUp /></el-icon>
                {{ trend.growth }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 技能热度区域 -->
    <div class="skills-section">
      <h3 class="section-title">
        <el-icon class="title-icon"><Star /></el-icon>
        技能热度
      </h3>
      <div class="skills-list">
        <div
          v-for="skill in hotSkills"
          :key="skill.name"
          class="skill-tag"
          :style="{ '--heat-level': skill.heatLevel }"
        >
          <span class="skill-name">{{ skill.name }}</span>
          <span class="skill-heat">🔥{{ skill.heat }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  TrendCharts,
  Document,
  User,
  Clock,
  Lightning,
  ArrowUp,
  Star,
} from '@element-plus/icons-vue'
import { courses, mockInstructors } from '@/data/mockData'
import { useCountUp, useScrollAnimation } from '@/composables/useIntersectionObserver'

// 响应式数据
const animateNumbers = ref(false)
const animatedTotalCourses = ref(0)
const animatedTotalStudents = ref(0)
const animatedTotalHours = ref(0)

// 计算统计数据
const totalCourses = computed(() => courses.length)
const totalStudents = computed(() => courses.reduce((sum, course) => sum + course.studentsCount, 0))
const totalHours = computed(() => {
  // 假设每个课程平均20小时，计算总学习时长
  const avgHoursPerCourse = 20
  const totalLearningHours = courses.reduce(
    (sum, course) => sum + course.studentsCount * avgHoursPerCourse,
    0,
  )
  return Math.floor(totalLearningHours / 1000) // 转换为千小时
})

// 实时数据
const onlineUsers = ref(1247)
const todayNewUsers = ref(89)

// 热门趋势课程
const trendingCourses = computed(() => {
  return courses
    .sort((a, b) => b.studentsCount - a.studentsCount)
    .slice(0, 4)
    .map((course, index) => ({
      id: course.id,
      title: course.title.length > 20 ? course.title.substring(0, 20) + '...' : course.title,
      studentsCount: course.studentsCount,
      growth: Math.floor(Math.random() * 50) + 10, // 模拟增长率
      growthClass: index < 2 ? 'high-growth' : 'normal-growth',
    }))
})

// 热门技能
const hotSkills = computed(() => {
  const skillsMap = new Map()

  courses.forEach((course) => {
    course.skills.forEach((skill) => {
      if (skillsMap.has(skill)) {
        skillsMap.set(skill, skillsMap.get(skill) + course.studentsCount)
      } else {
        skillsMap.set(skill, course.studentsCount)
      }
    })
  })

  return Array.from(skillsMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 6)
    .map(([name, count]) => ({
      name,
      heat: Math.floor(count / 1000),
      heatLevel: Math.min(count / 3000, 1), // 0-1之间的热度级别
    }))
})

// 数字动画函数
const animateNumber = (target: number, current: any, duration: number = 2000) => {
  const start = 0
  const increment = target / (duration / 16)

  const timer = setInterval(() => {
    current.value += increment
    if (current.value >= target) {
      current.value = target
      clearInterval(timer)
    }
  }, 16)
}

// 实时数据更新
const updateRealtimeData = () => {
  // 模拟在线用户数变化
  onlineUsers.value = 1200 + Math.floor(Math.random() * 100)

  // 模拟今日新增用户
  const hour = new Date().getHours()
  todayNewUsers.value = Math.floor((hour / 24) * 120) + Math.floor(Math.random() * 20)
}

// 组件挂载时启动动画
onMounted(() => {
  // 启动数字动画
  setTimeout(() => {
    animateNumbers.value = true
    animateNumber(totalCourses.value, animatedTotalCourses, 1500)
    animateNumber(totalStudents.value, animatedTotalStudents, 2000)
    animateNumber(totalHours.value, animatedTotalHours, 2500)
  }, 300)

  // 定期更新实时数据
  updateRealtimeData()
  setInterval(updateRealtimeData, 30000) // 每30秒更新一次
})
</script>

<style scoped lang="scss">
.learning-stats-panel {
  padding: 16px;
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  max-height: 600px; /* 限制最大高度以适应视口 */
  overflow-y: auto;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-primary);

    .title-icon {
      margin-right: 6px;
      color: var(--brand-primary);
    }
  }

  // 学习统计区域
  .stats-section {
    margin-bottom: 20px;

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;

      .stat-item {
        position: relative;
        padding: 12px;
        background: linear-gradient(
          135deg,
          var(--brand-primary-light),
          var(--brand-secondary-light)
        );
        border-radius: 8px;
        text-align: center;
        overflow: hidden;

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: var(--brand-primary);
          margin-bottom: 4px;
          transition: all 0.3s ease;

          &.animate {
            transform: scale(1.1);
          }
        }

        .stat-label {
          font-size: 11px;
          color: var(--text-secondary);
          font-weight: 500;
        }

        .stat-icon {
          position: absolute;
          top: 8px;
          right: 8px;
          opacity: 0.3;
          font-size: 20px;
          color: var(--brand-primary);
        }
      }
    }
  }

  // 实时动态区域
  .realtime-section {
    margin-bottom: 20px;

    .realtime-stats {
      display: flex;
      gap: 8px;

      .realtime-item {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 10px;
        background: var(--bg-secondary);
        border-radius: 6px;
        border: 1px solid var(--border-primary);

        .realtime-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 8px;
          animation: pulse 2s infinite;

          &.online {
            background: #67c23a;
          }

          &.new {
            background: var(--brand-primary);
          }
        }

        .realtime-content {
          .realtime-number {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            line-height: 1;
          }

          .realtime-label {
            font-size: 10px;
            color: var(--text-tertiary);
          }
        }
      }
    }
  }

  // 热门趋势区域
  .trending-section {
    margin-bottom: 20px;

    .trending-list {
      .trending-item {
        display: flex;
        align-items: center;
        padding: 8px;
        margin-bottom: 6px;
        background: var(--bg-secondary);
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          background: var(--brand-primary-light);
          transform: translateX(2px);
        }

        &.top-trend {
          background: linear-gradient(
            135deg,
            var(--brand-primary-light),
            var(--brand-secondary-light)
          );
          border: 1px solid var(--brand-primary);
        }

        .trend-rank {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--brand-primary);
          color: white;
          border-radius: 50%;
          font-size: 10px;
          font-weight: 600;
          margin-right: 8px;
        }

        .trend-content {
          flex: 1;

          .trend-title {
            font-size: 11px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
            line-height: 1.2;
          }

          .trend-stats {
            display: flex;
            align-items: center;
            gap: 6px;

            .trend-students {
              font-size: 9px;
              color: var(--text-tertiary);
            }

            .trend-growth {
              display: flex;
              align-items: center;
              font-size: 9px;
              font-weight: 600;

              &.high-growth {
                color: #67c23a;
              }

              &.normal-growth {
                color: var(--brand-primary);
              }

              .el-icon {
                margin-right: 2px;
                font-size: 8px;
              }
            }
          }
        }
      }
    }
  }

  // 技能热度区域
  .skills-section {
    .skills-list {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;

      .skill-tag {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        background: var(--bg-secondary);
        border-radius: 12px;
        border: 1px solid var(--border-primary);
        transition: all 0.2s ease;
        opacity: calc(0.6 + var(--heat-level) * 0.4);

        &:hover {
          transform: scale(1.05);
          background: var(--brand-primary-light);
        }

        .skill-name {
          font-size: 10px;
          color: var(--text-primary);
          margin-right: 4px;
        }

        .skill-heat {
          font-size: 9px;
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .learning-stats-panel {
    padding: 12px;

    .stats-section .stats-grid {
      grid-template-columns: 1fr 1fr;
      gap: 8px;

      .stat-item {
        padding: 8px;

        .stat-number {
          font-size: 18px;
        }

        .stat-label {
          font-size: 10px;
        }
      }
    }

    .realtime-section .realtime-stats {
      flex-direction: column;
      gap: 6px;
    }

    .trending-section .trending-list .trending-item {
      padding: 6px;

      .trend-content .trend-title {
        font-size: 10px;
      }
    }
  }
}
</style>
