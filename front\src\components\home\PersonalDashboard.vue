<template>
  <div class="personal-dashboard">
    <!-- 已登录状态 -->
    <div v-if="userStore.isLoggedIn" class="dashboard-content">
      <!-- 用户欢迎信息 -->
      <div class="user-welcome">
        <div class="user-avatar">
          <img :src="userStore.user?.avatar" :alt="userStore.user?.name" />
          <div class="level-badge">Lv.{{ userLevel }}</div>
        </div>
        <div class="user-info">
          <h3 class="user-name">{{ userStore.user?.name }}</h3>
          <p class="user-title">{{ userTitle }}</p>
          <div class="study-streak">
            <el-icon><Trophy /></el-icon>
            连续学习 {{ userStore.user?.learningProgress.consecutiveDays }} 天
          </div>
        </div>
      </div>

      <!-- 今日学习目标 -->
      <div class="daily-goals">
        <h4 class="section-title">
          <el-icon><Aim /></el-icon>
          今日目标
        </h4>
        <div class="goals-grid">
          <div class="goal-item">
            <div class="goal-progress">
              <el-progress
                type="circle"
                :percentage="todayTimeProgress"
                :width="60"
                :stroke-width="6"
                color="#67C23A"
              />
            </div>
            <div class="goal-info">
              <span class="goal-value"
                >{{ userStore.user?.learningProgress.todayStudyTime }}分钟</span
              >
              <span class="goal-label">学习时长</span>
            </div>
          </div>

          <div class="goal-item">
            <div class="goal-progress">
              <el-progress
                type="circle"
                :percentage="todayLessonsProgress"
                :width="60"
                :stroke-width="6"
                color="#409EFF"
              />
            </div>
            <div class="goal-info">
              <span class="goal-value">{{ todayCompletedLessons }}/{{ dailyLessonsTarget }}</span>
              <span class="goal-label">完成课时</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 继续学习 -->
      <div class="continue-learning">
        <h4 class="section-title">
          <el-icon><VideoPlay /></el-icon>
          继续学习
        </h4>
        <div v-if="recentCourse" class="recent-course hover-lift" @click="continueLearning">
          <div class="course-thumbnail hover-zoom">
            <img :src="recentCourse.courseImage" :alt="recentCourse.courseTitle" />
            <div class="play-overlay">
              <el-icon><VideoPlay /></el-icon>
            </div>
          </div>
          <div class="course-info">
            <h5 class="course-title">{{ recentCourse.courseTitle }}</h5>
            <p class="lesson-title">{{ recentCourse.lessonTitle }}</p>
            <div class="progress-info">
              <el-progress
                :percentage="recentCourse.progress"
                :show-text="false"
                :stroke-width="4"
              />
              <span class="progress-text">
                {{ recentCourse.completedLessons }}/{{ recentCourse.totalLessons }} 课时
              </span>
            </div>
          </div>
        </div>
        <div v-else class="no-recent">
          <el-icon><Document /></el-icon>
          <p>还没有学习记录，开始你的学习之旅吧！</p>
        </div>
      </div>

      <!-- 学习成就 -->
      <div class="achievements">
        <h4 class="section-title">
          <el-icon><Medal /></el-icon>
          学习成就
        </h4>
        <div class="achievement-stats">
          <div class="stat-item">
            <div class="stat-number">{{ userStore.user?.learningProgress.completedCourses }}</div>
            <div class="stat-label">完成课程</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ userStore.user?.learningProgress.certificates }}</div>
            <div class="stat-label">获得证书</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">
              {{ Math.floor(userStore.user?.learningProgress.totalStudyTime / 60) }}
            </div>
            <div class="stat-label">学习小时</div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <el-button
          type="primary"
          size="small"
          class="hover-scale"
          @click="goToMyCourses"
          :icon="Notebook"
        >
          我的课程
        </el-button>
        <el-button
          type="default"
          size="small"
          class="hover-scale"
          @click="goToLearningPlan"
          :icon="Calendar"
        >
          学习计划
        </el-button>
      </div>
    </div>

    <!-- 未登录状态 -->
    <div v-else class="login-prompt">
      <div class="login-content">
        <div class="login-icon">
          <el-icon><User /></el-icon>
        </div>
        <h3>开启学习之旅</h3>
        <p>登录后查看学习进度和个性化推荐</p>
        <div class="login-actions">
          <el-button type="primary" @click="showLogin">立即登录</el-button>
          <el-button type="default" @click="showRegister">免费注册</el-button>
        </div>
      </div>

      <!-- 学习路径导航 -->
      <div class="learning-paths">
        <h4>热门学习路径</h4>
        <div class="path-list">
          <div
            v-for="path in popularPaths"
            :key="path.id"
            class="path-item"
            @click="goToPath(path.id)"
          >
            <div class="path-icon">{{ path.icon }}</div>
            <div class="path-info">
              <span class="path-name">{{ path.name }}</span>
              <span class="path-courses">{{ path.courseCount }}门课程</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  User,
  VideoPlay,
  Aim,
  Trophy,
  Medal,
  Document,
  Notebook,
  Calendar,
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 今日学习目标
const dailyTimeTarget = ref(60) // 60分钟
const dailyLessonsTarget = ref(3) // 3课时
const todayCompletedLessons = ref(2) // 模拟今日已完成课时

// 计算属性
const userLevel = computed(() => {
  const completedCourses = userStore.user?.learningProgress.completedCourses || 0
  return Math.floor(completedCourses / 2) + 1
})

const userTitle = computed(() => {
  const level = userLevel.value
  if (level >= 10) return '学习专家'
  if (level >= 5) return '学习达人'
  if (level >= 3) return '学习新星'
  return '学习新手'
})

const todayTimeProgress = computed(() => {
  const todayTime = userStore.user?.learningProgress.todayStudyTime || 0
  return Math.min(Math.round((todayTime / dailyTimeTarget.value) * 100), 100)
})

const todayLessonsProgress = computed(() => {
  return Math.min(Math.round((todayCompletedLessons.value / dailyLessonsTarget.value) * 100), 100)
})

const recentCourse = computed(() => {
  return userStore.user?.recentLearning?.[0] || null
})

// 热门学习路径（未登录时显示）
const popularPaths = ref([
  { id: 1, name: '前端开发', icon: '💻', courseCount: 12 },
  { id: 2, name: 'Python编程', icon: '🐍', courseCount: 8 },
  { id: 3, name: '数据分析', icon: '📊', courseCount: 6 },
  { id: 4, name: 'UI设计', icon: '🎨', courseCount: 10 },
])

// 方法
const continueLearning = () => {
  if (recentCourse.value) {
    router.push(`/course/${recentCourse.value.courseId}`)
  }
}

const goToMyCourses = () => {
  router.push('/my-courses')
}

const goToLearningPlan = () => {
  router.push('/learning-plan')
}

const showLogin = () => {
  // 触发登录弹窗
  console.log('显示登录弹窗')
}

const showRegister = () => {
  // 触发注册弹窗
  console.log('显示注册弹窗')
}

const goToPath = (pathId: number) => {
  router.push(`/learning-path/${pathId}`)
}

// 组件挂载时初始化
onMounted(() => {
  // 可以在这里加载用户的学习数据
})
</script>

<style scoped lang="scss">
.personal-dashboard {
  height: 100%;
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  overflow: hidden;

  .dashboard-content {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .user-welcome {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-primary);

    .user-avatar {
      position: relative;
      width: 50px;
      height: 50px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .level-badge {
        position: absolute;
        bottom: -2px;
        right: -2px;
        background: var(--brand-primary);
        color: white;
        font-size: 10px;
        padding: 2px 4px;
        border-radius: 8px;
        font-weight: 600;
      }
    }

    .user-info {
      flex: 1;

      .user-name {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 4px 0;
      }

      .user-title {
        font-size: 12px;
        color: var(--text-secondary);
        margin: 0 0 6px 0;
      }

      .study-streak {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        color: var(--brand-primary);

        .el-icon {
          font-size: 12px;
        }
      }
    }
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px 0;

    .el-icon {
      font-size: 16px;
      color: var(--brand-primary);
    }
  }

  .daily-goals {
    .goals-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .goal-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        background: var(--bg-secondary);
        border-radius: 8px;
        border: 1px solid var(--border-primary);

        .goal-info {
          display: flex;
          flex-direction: column;

          .goal-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
          }

          .goal-label {
            font-size: 11px;
            color: var(--text-secondary);
          }
        }
      }
    }
  }

  .continue-learning {
    .recent-course {
      display: flex;
      gap: 12px;
      padding: 12px;
      background: var(--bg-secondary);
      border-radius: 8px;
      border: 1px solid var(--border-primary);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--brand-primary);
        transform: translateY(-1px);
      }

      .course-thumbnail {
        position: relative;
        width: 60px;
        height: 40px;
        border-radius: 6px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        &:hover .play-overlay {
          opacity: 1;
        }
      }

      .course-info {
        flex: 1;
        min-width: 0;

        .course-title {
          font-size: 13px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 4px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .lesson-title {
          font-size: 11px;
          color: var(--text-secondary);
          margin: 0 0 8px 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .progress-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .progress-text {
            font-size: 10px;
            color: var(--text-tertiary);
            white-space: nowrap;
          }
        }
      }
    }

    .no-recent {
      text-align: center;
      padding: 20px;
      color: var(--text-secondary);

      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
        color: var(--text-tertiary);
      }

      p {
        font-size: 12px;
        margin: 0;
      }
    }
  }

  .achievements {
    .achievement-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;

      .stat-item {
        text-align: center;
        padding: 12px 8px;
        background: var(--bg-secondary);
        border-radius: 8px;
        border: 1px solid var(--border-primary);

        .stat-number {
          font-size: 18px;
          font-weight: 600;
          color: var(--brand-primary);
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 10px;
          color: var(--text-secondary);
        }
      }
    }
  }

  .quick-actions {
    display: flex;
    gap: 8px;
    margin-top: auto;

    .el-button {
      flex: 1;
      font-size: 12px;
    }
  }

  // 未登录状态样式
  .login-prompt {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .login-content {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid var(--border-primary);

      .login-icon {
        width: 60px;
        height: 60px;
        background: var(--bg-secondary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        border: 1px solid var(--border-primary);

        .el-icon {
          font-size: 24px;
          color: var(--text-secondary);
        }
      }

      h3 {
        font-size: 16px;
        color: var(--text-primary);
        margin: 0 0 8px 0;
      }

      p {
        font-size: 12px;
        color: var(--text-secondary);
        margin: 0 0 16px 0;
        line-height: 1.4;
      }

      .login-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .el-button {
          font-size: 12px;
        }
      }
    }

    .learning-paths {
      flex: 1;
      padding-top: 16px;

      h4 {
        font-size: 14px;
        color: var(--text-primary);
        margin: 0 0 12px 0;
      }

      .path-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .path-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px;
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-primary);
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--brand-primary);
            transform: translateY(-1px);
          }

          .path-icon {
            font-size: 20px;
          }

          .path-info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .path-name {
              font-size: 13px;
              font-weight: 600;
              color: var(--text-primary);
            }

            .path-courses {
              font-size: 11px;
              color: var(--text-secondary);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .personal-dashboard {
    .dashboard-content {
      padding: 16px;
      gap: 16px;
    }

    .daily-goals .goals-grid {
      grid-template-columns: 1fr;
    }

    .achievements .achievement-stats {
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;

      .stat-item {
        padding: 8px 4px;

        .stat-number {
          font-size: 16px;
        }

        .stat-label {
          font-size: 9px;
        }
      }
    }
  }
}
</style>
