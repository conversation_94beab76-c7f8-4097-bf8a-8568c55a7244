<template>
  <section class="recommendation-section">
    <div class="container">
      <!-- 区域标题和标签切换 -->
      <div class="section-header">
        <div class="title-area">
          <h2 class="section-title">{{ currentSectionTitle }}</h2>
          <p class="section-subtitle">{{ currentSectionSubtitle }}</p>
        </div>
        <div class="recommendation-tabs">
          <div
            v-for="tab in recommendationTabs"
            :key="tab.id"
            class="tab-item hover-lift"
            :class="{ active: activeTab === tab.id }"
            @click="switchTab(tab.id)"
          >
            <div class="tab-icon">{{ tab.icon }}</div>
            <span class="tab-name">{{ tab.name }}</span>
            <div class="tab-badge" v-if="tab.badge">{{ tab.badge }}</div>
          </div>
        </div>
      </div>

      <!-- 推荐理由说明 -->
      <div class="recommendation-reason" v-if="currentReasonText">
        <div class="reason-content">
          <el-icon><InfoFilled /></el-icon>
          <span>{{ currentReasonText }}</span>
        </div>
      </div>

      <!-- 课程推荐网格 -->
      <div class="courses-grid" v-loading="loading">
        <CourseCard
          v-for="recommendation in displayedRecommendations"
          :key="recommendation.course.id"
          :course="recommendation.course"
          :show-recommendation-reason="true"
          :recommendation-text="recommendation.reason"
          @click="goToCourse"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && displayedRecommendations.length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon><Box /></el-icon>
        </div>
        <h3>暂无推荐课程</h3>
        <p>我们正在为您准备更多精彩内容</p>
        <el-button type="primary" @click="refreshRecommendations">刷新推荐</el-button>
      </div>

      <!-- 查看更多 -->
      <div class="section-footer" v-if="displayedRecommendations.length > 0">
        <el-button type="default" size="large" @click="viewMore" :loading="loadingMore">
          查看更多课程
          <el-icon><ArrowRight /></el-icon>
        </el-button>
        <div class="recommendation-stats">
          <span>已为您推荐 {{ displayedRecommendations.length }} 门课程</span>
          <span>•</span>
          <span>{{ totalRecommendations }} 门课程等待发现</span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useRecommendationStore } from '@/stores/recommendation'
import CourseCard from '@/components/common/CourseCard.vue'
import { InfoFilled, Box, ArrowRight } from '@element-plus/icons-vue'

const router = useRouter()
const recommendationStore = useRecommendationStore()

// 响应式数据
const activeTab = ref('collaborative')
const loading = ref(false)
const loadingMore = ref(false)
const displayCount = ref(8) // 初始显示8门课程

// 推荐类型标签配置
const recommendationTabs = ref([
  {
    id: 'collaborative',
    name: '协同推荐',
    icon: '👥',
    badge: 'HOT',
    description: '学习相同课程的用户还在学什么',
  },
  {
    id: 'trending',
    name: '热门趋势',
    icon: '🔥',
    badge: null,
    description: '当前最受欢迎的课程',
  },
  {
    id: 'skill-based',
    name: '技能进阶',
    icon: '🚀',
    badge: null,
    description: '基于您的技能栈推荐',
  },
  {
    id: 'popular',
    name: '综合热度',
    icon: '⭐',
    badge: null,
    description: '综合评分最高的课程',
  },
])

// 计算属性
const currentTab = computed(() => {
  return recommendationTabs.value.find((tab) => tab.id === activeTab.value)
})

const currentSectionTitle = computed(() => {
  const titles = {
    collaborative: '学习相同课程的用户还在学什么',
    trending: '热门趋势课程',
    'skill-based': '为您推荐的技能进阶课程',
    popular: '综合热度推荐',
  }
  return titles[activeTab.value] || '为你推荐'
})

const currentSectionSubtitle = computed(() => {
  return currentTab.value?.description || '基于智能算法为您精选优质课程'
})

const currentReasonText = computed(() => {
  const reasonTexts = {
    collaborative: '基于与您学习相似课程的用户行为分析，这些课程获得了92%的好评率',
    trending: '这些课程在过去7天内学习人数增长最快，平均完课率达85%',
    'skill-based': '根据您的技能标签匹配，这些课程将帮助您在相关领域更进一步',
    popular: '综合考虑评分、完课率、学习人数等因素，为您推荐最优质的课程',
  }
  return reasonTexts[activeTab.value] || ''
})

const allRecommendations = computed(() => {
  let courses = []
  switch (activeTab.value) {
    case 'collaborative':
      courses = recommendationStore.getCollaborativeRecommendations(20)
      break
    case 'trending':
      courses = recommendationStore.getTrendingCourses(20)
      break
    case 'skill-based':
      courses = recommendationStore.getRecommendationsByTags(
        ['Vue', 'JavaScript', 'TypeScript'],
        20,
      )
      break
    case 'popular':
      courses = recommendationStore.getPopularCourses(20)
      break
    default:
      courses = recommendationStore.getPopularCourses(20)
  }

  return courses.map((course) => ({
    course,
    reason: generateRecommendationReason(course, activeTab.value),
  }))
})

const displayedRecommendations = computed(() => {
  return allRecommendations.value.slice(0, displayCount.value)
})

const totalRecommendations = computed(() => {
  return allRecommendations.value.length
})

// 方法
const generateRecommendationReason = (course: any, type: string) => {
  const reasons = {
    collaborative: [
      `学习${course.category}的同学${Math.floor(Math.random() * 20) + 80}%都选择了这门课`,
      `与您学习路径相似的用户强烈推荐`,
      `同类课程学习者的首选进阶课程`,
      `${Math.floor(Math.random() * 500) + 1000}+同学正在学习相同技能栈`,
    ],
    trending: [
      `本周学习人数增长${Math.floor(Math.random() * 50) + 150}%`,
      `近期最受欢迎的${course.category}课程`,
      `7天内新增${Math.floor(Math.random() * 200) + 300}名学员`,
      `热度飙升，限时优惠中`,
    ],
    'skill-based': [
      `完美匹配您的${course.skills?.[0] || '技能'}技能栈`,
      `${course.level}级别，适合您当前水平`,
      `技能进阶必修课程`,
      `掌握${course.skills?.slice(0, 2).join('、') || '核心技能'}的最佳选择`,
    ],
    popular: [
      `综合评分${(Math.random() * 0.5 + 4.5).toFixed(1)}分`,
      `${Math.floor(Math.random() * 80) + 85}%完课率`,
      `${Math.floor(Math.random() * 5000) + 5000}+学员好评`,
      `口碑课程，值得信赖`,
    ],
  }

  const typeReasons = reasons[type] || reasons['popular']
  return typeReasons[Math.floor(Math.random() * typeReasons.length)]
}

const switchTab = async (tabId: string) => {
  if (tabId === activeTab.value) return

  loading.value = true
  activeTab.value = tabId
  displayCount.value = 8 // 重置显示数量

  // 模拟加载延迟
  await new Promise((resolve) => setTimeout(resolve, 500))
  loading.value = false
}

const goToCourse = (courseId: string) => {
  router.push(`/course/${courseId}`)
}

const viewMore = async () => {
  loadingMore.value = true

  // 模拟加载更多
  await new Promise((resolve) => setTimeout(resolve, 800))

  displayCount.value = Math.min(displayCount.value + 8, totalRecommendations.value)
  loadingMore.value = false
}

const refreshRecommendations = async () => {
  loading.value = true

  // 刷新推荐数据
  await recommendationStore.refreshRecommendations()

  await new Promise((resolve) => setTimeout(resolve, 500))
  loading.value = false
}

// 监听activeTab变化，更新URL参数（可选）
watch(activeTab, (newTab) => {
  // 可以在这里更新URL参数，方便用户分享和回访
  const url = new URL(window.location.href)
  url.searchParams.set('recommend', newTab)
  window.history.replaceState({}, '', url.toString())
})

// 组件挂载时初始化
onMounted(async () => {
  // 从URL参数读取推荐类型
  const urlParams = new URLSearchParams(window.location.search)
  const recommendType = urlParams.get('recommend')
  if (recommendType && recommendationTabs.value.some((tab) => tab.id === recommendType)) {
    activeTab.value = recommendType
  }

  // 初始加载推荐数据
  loading.value = true
  await recommendationStore.initializeRecommendations()
  loading.value = false
})
</script>

<style scoped lang="scss">
.recommendation-section {
  padding: 60px 0;
  background: var(--bg-primary);

  .container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    gap: 40px;

    .title-area {
      flex: 1;

      .section-title {
        font-size: 32px;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 8px 0;
        line-height: 1.2;
      }

      .section-subtitle {
        font-size: 16px;
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.5;
      }
    }

    .recommendation-tabs {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .tab-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: var(--bg-secondary);
        border: 1px solid var(--border-primary);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        &:hover {
          border-color: var(--brand-primary);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.active {
          background: var(--brand-primary);
          border-color: var(--brand-primary);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(var(--brand-primary-rgb), 0.3);

          .tab-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
          }
        }

        .tab-icon {
          font-size: 16px;
        }

        .tab-name {
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
        }

        .tab-badge {
          background: var(--brand-primary);
          color: white;
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 8px;
          font-weight: 600;
        }
      }
    }
  }

  .recommendation-reason {
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, var(--brand-primary-light), var(--brand-secondary-light));
    border-radius: 12px;
    border: 1px solid var(--brand-primary-light);

    .reason-content {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--brand-primary);
      font-size: 14px;
      font-weight: 500;

      .el-icon {
        font-size: 16px;
      }
    }
  }

  .courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);

    .empty-icon {
      font-size: 64px;
      color: var(--text-tertiary);
      margin-bottom: 16px;
    }

    h3 {
      font-size: 18px;
      color: var(--text-primary);
      margin: 0 0 8px 0;
    }

    p {
      font-size: 14px;
      margin: 0 0 24px 0;
    }
  }

  .section-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-primary);

    .el-button {
      margin-bottom: 16px;
    }

    .recommendation-stats {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
      color: var(--text-tertiary);
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .recommendation-section {
    .section-header {
      flex-direction: column;
      align-items: stretch;
      gap: 20px;

      .recommendation-tabs {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
}

@media (max-width: 768px) {
  .recommendation-section {
    padding: 40px 0;

    .section-header {
      .title-area {
        .section-title {
          font-size: 24px;
        }

        .section-subtitle {
          font-size: 14px;
        }
      }

      .recommendation-tabs {
        .tab-item {
          padding: 8px 12px;

          .tab-name {
            font-size: 12px;
          }
        }
      }
    }

    .courses-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
    }
  }
}
</style>
