<template>
  <div class="smart-recommendation-carousel">
    <el-carousel
      height="350px"
      :interval="6000"
      :autoplay="true"
      indicator-position="outside"
      arrow="hover"
    >
      <el-carousel-item
        v-for="(recommendation, index) in recommendedCourses"
        :key="recommendation.course.id"
        class="carousel-item"
      >
        <div class="recommendation-card hover-lift" @click="goToCourse(recommendation.course.id)">
          <!-- 社交证明区域 -->
          <div class="social-proof-section">
            <div class="learner-avatars">
              <div
                v-for="(avatar, idx) in recommendation.learnerAvatars"
                :key="idx"
                class="avatar-item hover-scale"
                :style="{ zIndex: 10 - idx }"
              >
                <img :src="avatar" :alt="`学习者${idx + 1}`" />
              </div>
              <div class="avatar-more">+{{ recommendation.totalLearners - 3 }}</div>
            </div>
            <div class="recommendation-text">
              <div class="main-reason">{{ recommendation.reason }}</div>
              <div class="social-stats">
                <span class="active-learners">
                  <el-icon><User /></el-icon>
                  {{ recommendation.activeLearners }}位同学正在学习
                </span>
                <span class="completion-rate">
                  <el-icon><TrophyBase /></el-icon>
                  {{ recommendation.completionRate }}%完成率
                </span>
              </div>
            </div>
          </div>

          <!-- 课程预览区域 -->
          <div class="course-preview">
            <div class="course-image hover-zoom">
              <img :src="recommendation.course.image" :alt="recommendation.course.title" />
              <div class="course-overlay">
                <div class="play-button">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="course-badges">
                  <span v-if="recommendation.course.isHot" class="badge hot">🔥 热门</span>
                  <span v-if="recommendation.course.badge" class="badge special">
                    {{ recommendation.course.badge }}
                  </span>
                </div>
              </div>
            </div>

            <div class="course-info">
              <h3 class="course-title">{{ recommendation.course.title }}</h3>
              <div class="course-meta">
                <span class="instructor">
                  <el-icon><User /></el-icon>
                  {{ recommendation.course.instructor?.name || recommendation.course.instructor }}
                </span>
                <span class="rating">
                  <el-icon><Star /></el-icon>
                  {{ recommendation.course.rating }}
                </span>
                <span class="students">
                  {{ formatNumber(recommendation.course.studentsCount) }}人学习
                </span>
              </div>
              <div class="course-price">
                <span class="current-price">¥{{ recommendation.course.price }}</span>
                <span v-if="recommendation.course.originalPrice" class="original-price">
                  ¥{{ recommendation.course.originalPrice }}
                </span>
              </div>
            </div>
          </div>

          <!-- 推荐标签 -->
          <div class="recommendation-badge">
            <el-icon><TrendCharts /></el-icon>
            智能推荐
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 推荐算法切换 -->
    <div class="algorithm-switcher">
      <div class="algorithm-tabs">
        <div
          v-for="(algo, key) in algorithmLabels"
          :key="key"
          class="algorithm-tab"
          :class="{ active: currentAlgorithm === key }"
          @click="switchAlgorithm(key)"
        >
          {{ algo }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useRecommendationStore } from '@/stores/recommendation'
import { User, Star, VideoPlay, TrendCharts, TrophyBase } from '@element-plus/icons-vue'

const router = useRouter()
const recommendationStore = useRecommendationStore()

// 当前推荐算法
const currentAlgorithm = ref<string>('trending')

// 算法标签映射
const algorithmLabels = {
  trending: '🔥 热门趋势',
  tags: '🎯 兴趣匹配',
  popular: '⭐ 综合热度',
  'similar-level': '📈 难度适配',
  collaborative: '👥 协同推荐',
}

// 获取推荐课程
const recommendedCourses = computed(() => {
  let courses = []

  // 根据算法类型获取不同的推荐结果
  switch (currentAlgorithm.value) {
    case 'trending':
      courses = recommendationStore.getTrendingCourses(5)
      break
    case 'popular':
      courses = recommendationStore.getPopularCourses(5)
      break
    case 'tags':
      // 使用第一个课程作为参考进行标签推荐
      const firstCourse = recommendationStore.getTrendingCourses(1)[0]
      if (firstCourse) {
        const recommendations = recommendationStore.getRecommendationsByTags(firstCourse.id, 5)
        courses = recommendations.map((r) => r.course)
      }
      break
    case 'similar-level':
      // 使用热门课程作为参考进行级别推荐
      const refCourse = recommendationStore.getTrendingCourses(1)[0]
      if (refCourse) {
        const recommendations = recommendationStore.getSimilarLevelCourses(refCourse.id, 5)
        courses = recommendations.map((r) => r.course)
      }
      break
    case 'collaborative':
      // 使用热门课程作为参考进行协同推荐
      const baseCourse = recommendationStore.getTrendingCourses(1)[0]
      if (baseCourse) {
        const recommendations = recommendationStore.getCollaborativeRecommendations(
          baseCourse.id,
          5,
        )
        courses = recommendations.map((r) => r.course)
      }
      break
    default:
      courses = recommendationStore.getTrendingCourses(5)
  }

  return courses.slice(0, 5).map((course) => ({
    course,
    reason: generateRecommendationReason(course, currentAlgorithm.value),
    learnerAvatars: generateLearnerAvatars(),
    totalLearners: course.studentsCount,
    activeLearners: Math.floor(course.studentsCount * 0.15) + Math.floor(Math.random() * 20),
    completionRate: Math.floor(Math.random() * 30) + 70,
  }))
})

// 生成推荐理由
const generateRecommendationReason = (course: any, algorithm: string) => {
  const firstSkill = course.skills && course.skills.length > 0 ? course.skills[0] : '编程'
  const reasons = {
    trending: `学习${firstSkill}的同学${Math.floor(Math.random() * 20) + 80}%都会选择这门课`,
    tags: `基于你的兴趣，这门${course.category}课程最适合你`,
    popular: `综合评分${course.rating}分，${Math.floor(course.studentsCount / 1000)}k+学员强烈推荐`,
    'similar-level': `${course.difficulty || course.level}难度，与你当前水平完美匹配`,
    collaborative: `和你相似的学员都在学这门课程`,
  }
  return reasons[algorithm] || reasons['trending']
}

// 生成学习者头像
const generateLearnerAvatars = () => {
  const avatars = [
    'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
  ]
  return avatars
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 切换推荐算法
const switchAlgorithm = (algorithm: string) => {
  currentAlgorithm.value = algorithm
}

// 跳转到课程详情
const goToCourse = (courseId: string) => {
  router.push(`/course/${courseId}`)
}

// 组件挂载时初始化
onMounted(() => {
  // 可以根据用户偏好设置默认算法
  currentAlgorithm.value = 'trending'
})
</script>

<style scoped lang="scss">
.smart-recommendation-carousel {
  position: relative;
  padding: 20px 0;

  .carousel-item {
    border-radius: 16px;
    overflow: hidden;
  }

  .recommendation-card {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%
      );
      pointer-events: none;
    }
  }

  .social-proof-section {
    margin-bottom: 16px;

    .learner-avatars {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .avatar-item {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid white;
        margin-left: -8px;
        overflow: hidden;

        &:first-child {
          margin-left: 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .avatar-more {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid white;
        margin-left: -8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: white;
        font-weight: 600;
      }
    }

    .recommendation-text {
      .main-reason {
        color: white;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .social-stats {
        display: flex;
        gap: 16px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);

        .active-learners,
        .completion-rate {
          display: flex;
          align-items: center;
          gap: 4px;

          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
  }

  .course-preview {
    flex: 1;
    display: flex;
    gap: 16px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(10px);

    .course-image {
      position: relative;
      width: 120px;
      height: 80px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .course-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .play-button {
          width: 32px;
          height: 32px;
          background: var(--brand-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
        }
      }

      &:hover .course-overlay {
        opacity: 1;
      }

      .course-badges {
        position: absolute;
        top: 4px;
        right: 4px;

        .badge {
          display: block;
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 4px;
          margin-bottom: 2px;

          &.hot {
            background: #ff4757;
            color: white;
          }

          &.special {
            background: var(--brand-primary);
            color: white;
          }
        }
      }
    }

    .course-info {
      flex: 1;

      .course-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
        line-height: 1.3;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .course-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        font-size: 12px;
        color: var(--text-secondary);

        .instructor,
        .rating,
        .students {
          display: flex;
          align-items: center;
          gap: 2px;

          .el-icon {
            font-size: 12px;
          }
        }

        .rating {
          color: #f39c12;
        }
      }

      .course-price {
        .current-price {
          font-size: 16px;
          font-weight: 600;
          color: var(--brand-primary);
        }

        .original-price {
          font-size: 12px;
          color: var(--text-tertiary);
          text-decoration: line-through;
          margin-left: 8px;
        }
      }
    }
  }

  .recommendation-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    backdrop-filter: blur(10px);

    .el-icon {
      font-size: 12px;
    }
  }

  .algorithm-switcher {
    margin-top: 16px;
    text-align: center;

    .algorithm-tabs {
      display: inline-flex;
      background: var(--bg-secondary);
      border-radius: 20px;
      padding: 4px;
      gap: 4px;

      .algorithm-tab {
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        color: var(--text-secondary);

        &:hover {
          background: var(--brand-primary-light);
          color: var(--brand-primary);
        }

        &.active {
          background: var(--brand-primary);
          color: white;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .smart-recommendation-carousel {
    .recommendation-card {
      padding: 16px;
    }

    .course-preview {
      flex-direction: column;
      gap: 12px;

      .course-image {
        width: 100%;
        height: 120px;
      }
    }

    .algorithm-switcher {
      .algorithm-tabs {
        flex-wrap: wrap;

        .algorithm-tab {
          font-size: 11px;
          padding: 4px 8px;
        }
      }
    }
  }
}
</style>
