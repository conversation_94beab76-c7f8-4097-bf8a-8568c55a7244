<template>
  <div class="recommendation-demo">
    <h3>推荐系统演示</h3>
    
    <!-- 基础推荐展示 -->
    <div class="demo-section">
      <h4>智能推荐课程</h4>
      <div class="courses-grid">
        <CourseCard
          v-for="rec in recommendations"
          :key="rec.course.id"
          :course="rec.course"
          :show-recommendation-reason="true"
          :recommendation-text="rec.reason"
          @click="handleCourseClick"
        />
      </div>
    </div>

    <!-- 热门课程展示 -->
    <div class="demo-section">
      <h4>热门课程</h4>
      <div class="courses-grid">
        <CourseCard
          v-for="course in trending"
          :key="course.id"
          :course="course"
          :show-recommendation-reason="true"
          :recommendation-text="`${course.studentsCount}人正在学习`"
          @click="handleCourseClick"
        />
      </div>
    </div>

    <!-- 推荐统计 -->
    <div class="stats">
      <p>推荐算法数量: 5种</p>
      <p>缓存命中率: 高效</p>
      <p>推荐准确性: 基于多维度算法</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRecommendationStore } from '@/stores/recommendation'
import CourseCard from '@/components/common/CourseCard.vue'
import type { RecommendationResult } from '@/stores/recommendation'

const recommendationStore = useRecommendationStore()

const recommendations = ref<RecommendationResult[]>([])
const trending = ref([])

onMounted(() => {
  // 获取基于第一个课程的推荐
  recommendations.value = recommendationStore.getRecommendationsByTags('1', 3)
  
  // 获取热门课程
  trending.value = recommendationStore.getTrendingCourses(3)
})

const handleCourseClick = (courseId: string) => {
  console.log('Course clicked:', courseId)
  // 可以在这里添加路由跳转逻辑
}
</script>

<style lang="scss" scoped>
.recommendation-demo {
  padding: 24px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  h3 {
    color: var(--text-primary);
    margin-bottom: 24px;
    text-align: center;
  }

  .demo-section {
    margin-bottom: 32px;

    h4 {
      color: var(--text-primary);
      margin-bottom: 16px;
      font-size: 18px;
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
    }
  }

  .stats {
    background: var(--bg-secondary);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid var(--brand-primary);

    p {
      color: var(--text-secondary);
      margin: 8px 0;
      font-size: 14px;
    }
  }
}
</style>
