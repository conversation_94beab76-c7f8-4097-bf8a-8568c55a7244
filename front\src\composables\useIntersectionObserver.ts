import { ref, onMounted, onUnmounted, type Ref } from 'vue'

/**
 * 交互观察器组合函数
 * 用于检测元素是否进入视口，触发动画或懒加载
 */

export interface UseIntersectionObserverOptions {
  // 根元素，默认为视口
  root?: Element | null
  // 根边距，用于扩展或缩小根的边界框
  rootMargin?: string
  // 阈值，元素可见性百分比
  threshold?: number | number[]
  // 是否只触发一次
  once?: boolean
  // 是否立即开始观察
  immediate?: boolean
}

export interface UseIntersectionObserverReturn {
  // 是否可见
  isVisible: Ref<boolean>
  // 停止观察
  stop: () => void
  // 开始观察
  start: () => void
  // 重新开始观察
  restart: () => void
}

/**
 * 使用交互观察器
 * @param target 目标元素或元素引用
 * @param callback 回调函数
 * @param options 配置选项
 */
export function useIntersectionObserver(
  target: Element | Ref<Element | undefined | null>,
  callback?: (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => void,
  options: UseIntersectionObserverOptions = {}
): UseIntersectionObserverReturn {
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0.1,
    once = false,
    immediate = true
  } = options

  const isVisible = ref(false)
  let observer: IntersectionObserver | null = null
  let hasTriggered = false

  const cleanup = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  const stop = () => {
    cleanup()
  }

  const start = () => {
    cleanup()

    const element = typeof target === 'object' && 'value' in target ? target.value : target
    if (!element) return

    observer = new IntersectionObserver(
      (entries, obs) => {
        const entry = entries[0]
        const isIntersecting = entry.isIntersecting

        isVisible.value = isIntersecting

        // 执行回调函数
        if (callback) {
          callback(entries, obs)
        }

        // 如果设置了只触发一次，且已经触发过，则停止观察
        if (once && isIntersecting && !hasTriggered) {
          hasTriggered = true
          stop()
        }
      },
      {
        root,
        rootMargin,
        threshold
      }
    )

    observer.observe(element)
  }

  const restart = () => {
    hasTriggered = false
    start()
  }

  // 组件挂载时开始观察
  onMounted(() => {
    if (immediate) {
      start()
    }
  })

  // 组件卸载时清理
  onUnmounted(() => {
    stop()
  })

  return {
    isVisible,
    stop,
    start,
    restart
  }
}

/**
 * 滚动动画组合函数
 * 专门用于滚动触发的动画效果
 */
export function useScrollAnimation(
  target: Element | Ref<Element | undefined | null>,
  animationClass = 'fade-in-up',
  options: UseIntersectionObserverOptions = {}
) {
  const defaultOptions: UseIntersectionObserverOptions = {
    threshold: 0.1,
    once: true,
    rootMargin: '0px 0px -50px 0px', // 提前50px触发
    ...options
  }

  const { isVisible } = useIntersectionObserver(
    target,
    (entries) => {
      const entry = entries[0]
      const element = entry.target as HTMLElement

      if (entry.isIntersecting) {
        // 添加动画类
        element.classList.add(animationClass)
        element.classList.add('revealed')
      }
    },
    defaultOptions
  )

  return { isVisible }
}

/**
 * 懒加载组合函数
 * 用于图片或组件的懒加载
 */
export function useLazyLoad(
  target: Element | Ref<Element | undefined | null>,
  callback: () => void,
  options: UseIntersectionObserverOptions = {}
) {
  const defaultOptions: UseIntersectionObserverOptions = {
    threshold: 0,
    once: true,
    rootMargin: '50px', // 提前50px加载
    ...options
  }

  const isLoaded = ref(false)

  const { isVisible } = useIntersectionObserver(
    target,
    (entries) => {
      const entry = entries[0]
      if (entry.isIntersecting && !isLoaded.value) {
        isLoaded.value = true
        callback()
      }
    },
    defaultOptions
  )

  return { isVisible, isLoaded }
}

/**
 * 数字滚动动画组合函数
 * 用于数字从0滚动到目标值的动画效果
 */
export function useCountUp(
  target: number,
  duration = 2000,
  startValue = 0,
  easing = 'easeOutCubic'
) {
  const current = ref(startValue)
  const isAnimating = ref(false)

  const easingFunctions = {
    linear: (t: number) => t,
    easeInQuad: (t: number) => t * t,
    easeOutQuad: (t: number) => t * (2 - t),
    easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
    easeInCubic: (t: number) => t * t * t,
    easeOutCubic: (t: number) => (--t) * t * t + 1,
    easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  }

  const start = () => {
    if (isAnimating.value) return

    isAnimating.value = true
    const startTime = Date.now()
    const startVal = current.value
    const endVal = target
    const change = endVal - startVal

    const animate = () => {
      const now = Date.now()
      const elapsed = now - startTime
      const progress = Math.min(elapsed / duration, 1)

      const easingFunc = easingFunctions[easing] || easingFunctions.easeOutCubic
      const easedProgress = easingFunc(progress)

      current.value = Math.round(startVal + change * easedProgress)

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        isAnimating.value = false
      }
    }

    requestAnimationFrame(animate)
  }

  const reset = () => {
    current.value = startValue
    isAnimating.value = false
  }

  return {
    current,
    isAnimating,
    start,
    reset
  }
}

/**
 * 防抖组合函数
 * 用于防止频繁触发的操作
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay = 300
): [(...args: Parameters<T>) => void, () => void] {
  let timeoutId: number | null = null

  const debouncedFn = (...args: Parameters<T>) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }
    timeoutId = window.setTimeout(() => {
      fn(...args)
      timeoutId = null
    }, delay)
  }

  const cancel = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  return [debouncedFn, cancel]
}

/**
 * 节流组合函数
 * 用于限制函数执行频率
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay = 300
): [(...args: Parameters<T>) => void, () => void] {
  let lastExecTime = 0
  let timeoutId: number | null = null

  const throttledFn = (...args: Parameters<T>) => {
    const now = Date.now()

    if (now - lastExecTime >= delay) {
      fn(...args)
      lastExecTime = now
    } else {
      if (timeoutId !== null) {
        clearTimeout(timeoutId)
      }
      timeoutId = window.setTimeout(() => {
        fn(...args)
        lastExecTime = Date.now()
        timeoutId = null
      }, delay - (now - lastExecTime))
    }
  }

  const cancel = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  return [throttledFn, cancel]
}

/**
 * 平滑滚动组合函数
 */
export function useSmoothScroll() {
  const scrollTo = (
    target: number | Element | string,
    options: {
      duration?: number
      easing?: keyof typeof easingFunctions
      offset?: number
    } = {}
  ) => {
    const { duration = 800, easing = 'easeInOutCubic', offset = 0 } = options

    let targetY: number

    if (typeof target === 'number') {
      targetY = target
    } else if (typeof target === 'string') {
      const element = document.querySelector(target)
      if (!element) return
      targetY = element.getBoundingClientRect().top + window.pageYOffset
    } else {
      targetY = target.getBoundingClientRect().top + window.pageYOffset
    }

    targetY += offset

    const startY = window.pageYOffset
    const distance = targetY - startY
    const startTime = Date.now()

    const easingFunctions = {
      linear: (t: number) => t,
      easeInQuad: (t: number) => t * t,
      easeOutQuad: (t: number) => t * (2 - t),
      easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
      easeInCubic: (t: number) => t * t * t,
      easeOutCubic: (t: number) => (--t) * t * t + 1,
      easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
    }

    const scroll = () => {
      const now = Date.now()
      const elapsed = now - startTime
      const progress = Math.min(elapsed / duration, 1)

      const easingFunc = easingFunctions[easing]
      const easedProgress = easingFunc(progress)

      window.scrollTo(0, startY + distance * easedProgress)

      if (progress < 1) {
        requestAnimationFrame(scroll)
      }
    }

    requestAnimationFrame(scroll)
  }

  return { scrollTo }
}
