import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（例如浏览器前进/后退），则返回到该位置
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到页面顶部
    return { top: 0, behavior: 'smooth' }
  },
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/test-learning-stats',
      name: 'test-learning-stats',
      component: () => import('../views/TestLearningStatsPanel.vue'),
    },
    {
      path: '/test-smart-carousel',
      name: 'test-smart-carousel',
      component: () => import('../views/TestSmartCarousel.vue'),
    },
    {
      path: '/test-personal-dashboard',
      name: 'test-personal-dashboard',
      component: () => import('../views/TestPersonalDashboard.vue'),
    },
    {
      path: '/test-recommendation-section',
      name: 'test-recommendation-section',
      component: () => import('../views/TestRecommendationSection.vue'),
    },
    {
      path: '/paths',
      name: 'learning-paths',
      component: () => import('../views/LearningPathsOverviewView.vue'),
    },
    {
      path: '/path/:id',
      name: 'learning-path-detail',
      component: () => import('../views/LearningPathDetailView.vue'),
    },
    {
      path: '/course/:id',
      name: 'course-detail',
      component: () => import('../views/CourseDetailView.vue'),
    },
    {
      path: '/course/:courseId/lesson/:lessonId',
      name: 'lesson',
      component: () => import('../views/LessonView.vue'),
    },
    {
      path: '/courses',
      name: 'courses',
      component: () => import('../views/CoursesView.vue'),
    },
    {
      path: '/instructors',
      name: 'instructors',
      component: () => import('../views/InstructorsView.vue'),
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
    },
    {
      path: '/progress',
      name: 'progress',
      component: () => import('../views/ProgressView.vue'),
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
    },
    {
      path: '/test/course-card',
      name: 'course-card-test',
      component: () => import('../views/CourseCardTestView.vue'),
    },
    {
      path: '/test/recommendation',
      name: 'recommendation-test',
      component: () => import('../views/RecommendationTestView.vue'),
    },
    {
      path: '/test/animations',
      name: 'test-animations',
      component: () => import('../views/TestAnimations.vue'),
    },
  ],
})

export default router
