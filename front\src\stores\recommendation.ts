import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Course } from '@/types/course'
import { courses } from '@/data/mockData'

// 推荐类型枚举
export enum RecommendationType {
  TAGS_BASED = 'tags_based',
  TRENDING = 'trending',
  POPULAR = 'popular',
  SIMILAR_LEVEL = 'similar_level',
  COLLABORATIVE = 'collaborative',
}

// 推荐结果接口
export interface RecommendationResult {
  course: Course
  reason: string
  score: number
  type: RecommendationType
}

// 推荐缓存接口
interface RecommendationCache {
  [key: string]: {
    data: RecommendationResult[]
    timestamp: number
    expiry: number
  }
}

export const useRecommendationStore = defineStore('recommendation', () => {
  // 状态
  const recommendedCourses = ref<RecommendationResult[]>([])
  const trendingCourses = ref<Course[]>([])
  const popularCourses = ref<Course[]>([])
  const cache = ref<RecommendationCache>({})
  const isLoading = ref(false)

  // 缓存过期时间（5分钟）
  const CACHE_EXPIRY = 5 * 60 * 1000

  // 检查缓存是否有效
  const isCacheValid = (key: string): boolean => {
    const cached = cache.value[key]
    if (!cached) return false
    return Date.now() < cached.timestamp + cached.expiry
  }

  // 设置缓存
  const setCache = (key: string, data: RecommendationResult[]) => {
    cache.value[key] = {
      data,
      timestamp: Date.now(),
      expiry: CACHE_EXPIRY,
    }
  }

  // 获取缓存
  const getCache = (key: string): RecommendationResult[] | null => {
    if (isCacheValid(key)) {
      return cache.value[key].data
    }
    return null
  }

  // 基于技能标签的推荐算法
  const getRecommendationsByTags = (
    courseId: string,
    limit: number = 4,
  ): RecommendationResult[] => {
    const cacheKey = `tags_${courseId}_${limit}`
    const cached = getCache(cacheKey)
    if (cached) return cached

    const targetCourse = courses.find((c) => c.id === courseId)
    if (!targetCourse) return []

    const recommendations: RecommendationResult[] = []
    const targetSkills = targetCourse.skills

    courses.forEach((course) => {
      if (course.id === courseId) return

      // 计算技能匹配度
      const commonSkills = course.skills.filter((skill) => targetSkills.includes(skill))

      if (commonSkills.length > 0) {
        const matchScore = (commonSkills.length / targetSkills.length) * 100
        const reason = generateTagsBasedReason(course, commonSkills, targetCourse)

        recommendations.push({
          course,
          reason,
          score: matchScore,
          type: RecommendationType.TAGS_BASED,
        })
      }
    })

    // 按匹配度排序并限制数量
    const result = recommendations.sort((a, b) => b.score - a.score).slice(0, limit)

    setCache(cacheKey, result)
    return result
  }

  // 基于热度的推荐算法
  const getTrendingCourses = (limit: number = 6): Course[] => {
    const cacheKey = `trending_${limit}`

    if (isCacheValid(cacheKey)) {
      return cache.value[cacheKey].data.map((r) => r.course)
    }

    // 综合评分算法：学员数量 * 0.6 + 评分 * 20 * 0.4
    const trending = courses
      .map((course) => ({
        course,
        trendingScore: course.studentsCount * 0.6 + course.rating * 20 * 0.4,
      }))
      .sort((a, b) => b.trendingScore - a.trendingScore)
      .slice(0, limit)
      .map((item) => item.course)

    // 缓存结果
    const cacheData = trending.map((course) => ({
      course,
      reason: generateTrendingReason(course),
      score: 0,
      type: RecommendationType.TRENDING,
    }))
    setCache(cacheKey, cacheData)

    trendingCourses.value = trending
    return trending
  }

  // 基于综合热度的推荐算法
  const getPopularCourses = (limit: number = 8): Course[] => {
    const cacheKey = `popular_${limit}`

    if (isCacheValid(cacheKey)) {
      return cache.value[cacheKey].data.map((r) => r.course)
    }

    // 综合热度算法：评分权重40% + 学员数权重30% + 是否热门权重30%
    const popular = courses
      .map((course) => {
        const ratingScore = course.rating * 20 // 评分转换为100分制
        const studentsScore = Math.min(course.studentsCount / 100, 100) // 学员数标准化
        const hotBonus = course.isHot ? 20 : 0 // 热门课程加分

        const popularityScore = ratingScore * 0.4 + studentsScore * 0.3 + hotBonus * 0.3

        return {
          course,
          popularityScore,
        }
      })
      .sort((a, b) => b.popularityScore - a.popularityScore)
      .slice(0, limit)
      .map((item) => item.course)

    // 缓存结果
    const cacheData = popular.map((course) => ({
      course,
      reason: generatePopularReason(course),
      score: 0,
      type: RecommendationType.POPULAR,
    }))
    setCache(cacheKey, cacheData)

    popularCourses.value = popular
    return popular
  }

  // 基于相似难度级别的推荐
  const getSimilarLevelCourses = (courseId: string, limit: number = 4): RecommendationResult[] => {
    const cacheKey = `level_${courseId}_${limit}`
    const cached = getCache(cacheKey)
    if (cached) return cached

    const targetCourse = courses.find((c) => c.id === courseId)
    if (!targetCourse) return []

    const recommendations: RecommendationResult[] = []
    const targetLevel = targetCourse.level

    courses.forEach((course) => {
      if (course.id === courseId) return

      // 相同级别或进阶级别推荐
      const isRecommended =
        course.level === targetLevel ||
        (targetLevel === 'beginner' && course.level === 'intermediate') ||
        (targetLevel === 'intermediate' && course.level === 'advanced')

      if (isRecommended) {
        const reason = generateLevelBasedReason(course, targetLevel)
        recommendations.push({
          course,
          reason,
          score: course.level === targetLevel ? 100 : 80,
          type: RecommendationType.SIMILAR_LEVEL,
        })
      }
    })

    const result = recommendations.sort((a, b) => b.score - a.score).slice(0, limit)

    setCache(cacheKey, result)
    return result
  }

  // 协同过滤推荐（模拟）
  const getCollaborativeRecommendations = (
    courseId: string,
    limit: number = 4,
  ): RecommendationResult[] => {
    const cacheKey = `collaborative_${courseId}_${limit}`
    const cached = getCache(cacheKey)
    if (cached) return cached

    const targetCourse = courses.find((c) => c.id === courseId)
    if (!targetCourse) return []

    // 模拟协同过滤：基于相同分类和相似评分的课程
    const recommendations: RecommendationResult[] = []

    courses.forEach((course) => {
      if (course.id === courseId) return

      const isSameCategory = course.category === targetCourse.category
      const ratingDiff = Math.abs(course.rating - targetCourse.rating)

      if (isSameCategory && ratingDiff <= 0.5) {
        const reason = generateCollaborativeReason(course, targetCourse)
        const score = 100 - ratingDiff * 20 // 评分差异越小，推荐分数越高

        recommendations.push({
          course,
          reason,
          score,
          type: RecommendationType.COLLABORATIVE,
        })
      }
    })

    const result = recommendations.sort((a, b) => b.score - a.score).slice(0, limit)

    setCache(cacheKey, result)
    return result
  }

  // 生成基于标签的推荐理由
  const generateTagsBasedReason = (
    course: Course,
    commonSkills: string[],
    targetCourse: Course,
  ): string => {
    const skillsText = commonSkills.slice(0, 2).join('、')
    const percentage = Math.floor(Math.random() * 15) + 85 // 85-99%

    const reasons = [
      `学习${skillsText}的同学${percentage}%都选择了这门课`,
      `掌握${skillsText}技能的进阶首选`,
      `${skillsText}学习者的热门选择`,
      `与《${targetCourse.title}》技能互补的优质课程`,
    ]

    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  // 生成热门推荐理由
  const generateTrendingReason = (course: Course): string => {
    const reasons = [
      `${course.studentsCount}人正在学习，口碑爆棚`,
      `本周学习人数上升最快的课程`,
      `高评分热门课程，不容错过`,
      `最受欢迎的${course.category}课程之一`,
    ]

    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  // 生成热度推荐理由
  const generatePopularReason = (course: Course): string => {
    const reasons = [
      `${course.rating}分高评价，${course.studentsCount}人推荐`,
      `综合评价最高的优质课程`,
      `学员满意度极高的精品课程`,
      `口碑与实力并存的明星课程`,
    ]

    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  // 生成基于级别的推荐理由
  const generateLevelBasedReason = (course: Course, targetLevel: string): string => {
    if (course.level === targetLevel) {
      return `同等难度的优质课程选择`
    } else {
      const levelMap = {
        beginner: '入门',
        intermediate: '进阶',
        advanced: '高级',
      }
      return `${levelMap[course.level as keyof typeof levelMap]}学习的理想选择`
    }
  }

  // 生成协同过滤推荐理由
  const generateCollaborativeReason = (course: Course, targetCourse: Course): string => {
    const reasons = [
      `学习《${targetCourse.title}》的同学还选择了这门课`,
      `与你的学习兴趣高度匹配`,
      `同类课程中的优质选择`,
      `相似学习路径的推荐课程`,
    ]

    return reasons[Math.floor(Math.random() * reasons.length)]
  }

  // 清除过期缓存
  const clearExpiredCache = () => {
    const now = Date.now()
    Object.keys(cache.value).forEach((key) => {
      const cached = cache.value[key]
      if (now >= cached.timestamp + cached.expiry) {
        delete cache.value[key]
      }
    })
  }

  // 清除所有缓存
  const clearAllCache = () => {
    cache.value = {}
  }

  // 计算属性
  const hasRecommendations = computed(() => recommendedCourses.value.length > 0)
  const cacheSize = computed(() => Object.keys(cache.value).length)

  // 初始化推荐数据
  const initializeRecommendations = async () => {
    isLoading.value = true
    try {
      // 获取默认推荐（热门课程）
      const trending = getTrendingCourses(8)
      trendingCourses.value = trending

      // 获取热门课程
      const popular = getPopularCourses(8)
      popularCourses.value = popular

      // 设置默认推荐为热门课程
      recommendedCourses.value = trending
    } catch (error) {
      console.error('初始化推荐数据失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    recommendedCourses,
    trendingCourses,
    popularCourses,
    isLoading,

    // 计算属性
    hasRecommendations,
    cacheSize,

    // 方法
    getRecommendationsByTags,
    getTrendingCourses,
    getPopularCourses,
    getSimilarLevelCourses,
    getCollaborativeRecommendations,
    initializeRecommendations,
    clearExpiredCache,
    clearAllCache,
  }
})
