<template>
  <div class="course-card-test">
    <div class="container">
      <h1>CourseCard 组件测试</h1>

      <section class="test-section">
        <h2>基础样式测试</h2>
        <div class="cards-grid">
          <CourseCard :course="testCourse" @click="handleCourseClick" />
        </div>
      </section>

      <section class="test-section">
        <h2>推荐理由标签测试</h2>
        <div class="cards-grid">
          <CourseCard
            :course="testCourse"
            :show-recommendation-reason="true"
            recommendation-text="学习Vue3的同学92%都选择了这门课"
            @click="handleCourseClick"
          />
        </div>
      </section>

      <section class="test-section">
        <h2>紧凑尺寸测试</h2>
        <div class="cards-grid">
          <CourseCard :course="testCourse" size="compact" @click="handleCourseClick" />
          <CourseCard
            :course="testCourse"
            size="compact"
            :show-recommendation-reason="true"
            recommendation-text="热门推荐"
            @click="handleCourseClick"
          />
        </div>
      </section>

      <section class="test-section">
        <h2>不同课程数据测试</h2>
        <div class="cards-grid">
          <CourseCard
            v-for="course in testCourses"
            :key="course.id"
            :course="course"
            @click="handleCourseClick"
          />
        </div>
      </section>

      <section class="test-section">
        <h2>标签去重测试</h2>
        <p class="test-description">测试课程标签的去重逻辑，确保不会重复显示相同的标签</p>
        <div class="cards-grid">
          <CourseCard :course="duplicateTagCourse" @click="handleCourseClick" />
        </div>
      </section>

      <div class="click-result" v-if="clickedCourseId">
        <p>点击了课程ID: {{ clickedCourseId }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CourseCard from '@/components/common/CourseCard.vue'
import { courses } from '@/data/mockData'
import type { Course } from '@/types/course'

const clickedCourseId = ref<string>('')

// 测试用的课程数据
const testCourse: Course = courses[0]
const testCourses = courses.slice(0, 4)

// 创建一个有重复标签的测试课程
const duplicateTagCourse: Course = {
  ...courses[0],
  id: 'test-duplicate',
  title: '标签去重测试课程',
  badge: '热门', // 自定义badge设为"热门"
  isHot: true, // 同时设置isHot为true
  isNew: false,
}

const handleCourseClick = (courseId: string) => {
  clickedCourseId.value = courseId
  console.log('Course clicked:', courseId)
}
</script>

<style lang="scss" scoped>
.course-card-test {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  h1 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 40px;
  }

  .test-section {
    margin-bottom: 60px;

    h2 {
      color: var(--text-primary);
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 2px solid var(--brand-primary);
    }

    .test-description {
      color: var(--text-secondary);
      font-size: 14px;
      margin-bottom: 16px;
      padding: 12px;
      background: var(--bg-tertiary);
      border-radius: 8px;
      border-left: 4px solid var(--brand-primary);
    }

    .cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
      margin-bottom: 20px;
    }
  }

  .click-result {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--brand-primary);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    p {
      margin: 0;
      font-weight: 500;
    }
  }
}
</style>
