<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { mockCategories, mockInstructors } from '@/data/mockData'
import HeroSection from '@/components/home/<USER>'
import LearningStatsPanel from '@/components/home/<USER>'
import SmartRecommendationCarousel from '@/components/home/<USER>'
import PersonalDashboard from '@/components/home/<USER>'
import RecommendationSection from '@/components/home/<USER>'

const router = useRouter()

// 使用Mock数据
const categoriesList = ref(mockCategories)
const instructorsList = ref(mockInstructors)

const goToCategory = (path: string) => {
  router.push(path)
}
</script>

<template>
  <div class="home-view">
    <!-- Hero区域 - 使用组件化结构 -->
    <HeroSection>
      <!-- 左侧学习统计面板 -->
      <template #left-sidebar>
        <LearningStatsPanel />
      </template>

      <!-- 中间智能推荐轮播 -->
      <template #center-content>
        <SmartRecommendationCarousel />
      </template>

      <!-- 右侧个人学习仪表板 -->
      <template #right-sidebar>
        <PersonalDashboard />
      </template>
    </HeroSection>

    <!-- 学习分类导航 -->
    <section class="categories-section">
      <div class="container">
        <h2 class="section-title">免费学习</h2>
        <div class="categories-grid">
          <div
            v-for="category in categoriesList"
            :key="category.id"
            class="category-item"
            @click="goToCategory(category.path)"
          >
            <div class="category-icon">
              <component :is="category.icon" />
            </div>
            <h3 class="category-title">{{ category.title }}</h3>
            <p class="category-desc">{{ category.description }}</p>
            <div class="category-stats">
              <span>{{ category.coursesCount }}门课程</span>
              <span>{{ category.studentsCount }}人学习</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 智能推荐课程区域 -->
    <RecommendationSection />

    <!-- 名师介绍 -->
    <section class="instructors-section">
      <div class="container">
        <h2 class="section-title text-center">名师介绍</h2>
        <div class="instructors-grid">
          <div v-for="instructor in instructorsList" :key="instructor.id" class="instructor-card">
            <div class="instructor-avatar">
              <img :src="instructor.avatar" :alt="instructor.name" />
            </div>
            <div class="instructor-info">
              <h3 class="instructor-name">{{ instructor.name }}</h3>
              <p class="instructor-title">{{ instructor.title }}</p>
              <p class="instructor-desc">{{ instructor.description }}</p>
              <div class="instructor-stats">
                <span class="stat-item">{{ instructor.coursesCount }}门课程</span>
                <span class="stat-item">{{ instructor.studentsCount }}学员</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">
.home-view {
  // 首页使用统一的宽屏容器样式
  .container {
    max-width: var(--container-max-width) !important;
    margin: 0 auto !important;
    padding: 0 var(--container-padding) !important;
  }

  // Hero区域样式现在在HeroSection组件中
  .banner-item {
    height: 300px;
    border-radius: 12px;
    margin: 0 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform var(--transition-base);

    &:hover {
      transform: scale(1.02);
    }

    .banner-image-full {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 12px;
    }
  }

  .sidebar-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .sidebar-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 8px;
    }
  }

  .category-nav {
    list-style: none;
    padding: 0;
    margin: 0;

    .category-nav-item {
      margin-bottom: 4px;

      .category-link {
        display: flex;
        align-items: center;
        padding: 6px 8px;
        border-radius: 6px;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--bg-secondary);
          color: var(--text-primary);
        }

        .category-icon {
          font-size: 16px;
          margin-right: 6px;
          color: var(--primary);
        }

        .category-name {
          font-size: 13px;
          font-weight: 500;
          flex: 1;
        }

        .category-count {
          font-size: 11px;
          color: var(--text-tertiary);
          background-color: var(--bg-secondary);
          padding: 1px 4px;
          border-radius: 6px;
        }
      }
    }
  }

  .learning-paths-nav {
    list-style: none;
    padding: 0;
    margin: 0;

    .path-nav-item {
      margin-bottom: 4px;

      .path-link {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 8px;
        border-radius: 6px;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--bg-secondary);
          color: var(--text-primary);
        }

        .path-info {
          .path-name {
            font-size: 13px;
            font-weight: 500;
            margin-bottom: 1px;
            display: block;
          }
          .path-level {
            font-size: 11px;
            color: var(--primary);
          }
        }

        .path-courses {
          font-size: 11px;
          color: var(--text-tertiary);
        }
      }
    }
  }

  .user-panel {
    .user-welcome {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 10px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .user-name {
          font-size: 14px;
          font-weight: 600;
          color: var(--text-primary);
        }
        .user-level {
          font-size: 11px;
          color: var(--primary);
          background: var(--bg-secondary);
          padding: 2px 6px;
          border-radius: 10px;
        }
      }
    }

    .quick-actions-panel {
      margin-bottom: 16px;

      .panel-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-primary);
      }

      .action-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px;

        .action-btn {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px 6px;
          border-radius: 6px;
          color: var(--text-secondary);
          text-decoration: none;
          transition: all 0.2s ease;
          font-size: 12px;

          &:hover {
            background-color: var(--bg-secondary);
            color: var(--text-primary);
          }

          .el-icon {
            font-size: 16px;
            margin-bottom: 4px;
            color: var(--primary);
          }

          span {
            font-size: 11px;
          }
        }
      }
    }

    .learning-progress {
      .panel-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-primary);
      }

      .progress-info {
        .progress-bar {
          width: 100%;
          height: 6px;
          background-color: var(--bg-secondary);
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 6px;

          .progress-fill {
            height: 100%;
            background: linear-gradient(to right, var(--primary), var(--secondary));
            border-radius: 3px;
          }
        }

        .progress-text {
          font-size: 11px;
          color: var(--text-tertiary);
        }
      }
    }

    // 继续学习面板
    .continue-learning-panel {
      margin-bottom: 16px;

      .panel-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-primary);
      }

      .recent-courses {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .recent-course-item {
        display: flex;
        gap: 8px;
        padding: 8px;
        background: var(--bg-secondary);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--background-light);
          transform: translateY(-1px);
        }

        .course-thumbnail {
          position: relative;
          width: 40px;
          height: 30px;
          border-radius: 4px;
          overflow: hidden;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s ease;

            .el-icon {
              color: white;
              font-size: 12px;
            }
          }

          &:hover .play-overlay {
            opacity: 1;
          }
        }

        .course-details {
          flex: 1;
          min-width: 0;

          .course-title {
            font-size: 12px;
            font-weight: 600;
            margin: 0 0 2px 0;
            color: var(--text-primary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .lesson-title {
            font-size: 10px;
            color: var(--text-secondary);
            margin: 0 0 4px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .course-progress {
            display: flex;
            align-items: center;
            gap: 4px;

            :deep(.el-progress) {
              flex: 1;
              .el-progress-bar__outer {
                height: 3px;
                background-color: var(--border-light);
              }
            }

            .progress-text {
              font-size: 9px;
              color: var(--text-secondary);
              white-space: nowrap;
            }
          }
        }
      }

      .no-recent-learning {
        text-align: center;
        padding: 16px;
        color: var(--text-secondary);

        p {
          margin: 0 0 8px 0;
          font-size: 12px;
        }
      }
    }

    // 今日学习目标面板
    .daily-goal-panel {
      margin-bottom: 16px;

      .panel-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-primary);
      }

      .goal-content {
        .goal-progress {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 8px;

          .goal-ring {
            flex-shrink: 0;
          }

          .goal-details {
            flex: 1;

            .goal-text {
              display: flex;
              align-items: baseline;
              gap: 2px;
              margin-bottom: 2px;

              .current {
                font-size: 16px;
                font-weight: bold;
                color: var(--primary-color);
              }

              .separator {
                font-size: 12px;
                color: var(--text-secondary);
              }

              .target {
                font-size: 12px;
                color: var(--text-secondary);
              }

              .unit {
                font-size: 10px;
                color: var(--text-secondary);
              }
            }

            .goal-title {
              font-size: 10px;
              color: var(--text-secondary);
              margin: 0;
            }
          }
        }

        .streak-info {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 6px 8px;
          background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
          border-radius: 4px;
          color: white;

          .streak-icon {
            font-size: 12px;
          }

          .streak-text {
            font-size: 10px;
            font-weight: 500;
          }
        }
      }
    }

    // 学习成就面板
    .achievements-panel {
      margin-bottom: 16px;

      .panel-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-primary);
      }

      .achievements-grid {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .achievement-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 8px;
        background: var(--bg-secondary);
        border-radius: 4px;
        opacity: 0.6;
        transition: all 0.2s ease;

        &.earned {
          opacity: 1;
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          color: white;

          .achievement-icon {
            filter: none;
          }

          .achievement-title {
            color: white;
          }
        }

        .achievement-icon {
          font-size: 16px;
          filter: grayscale(1);
        }

        .achievement-info {
          flex: 1;
          min-width: 0;

          .achievement-title {
            font-size: 11px;
            font-weight: 500;
            margin-bottom: 2px;
            color: var(--text-primary);
          }

          .achievement-progress {
            :deep(.el-progress-bar__outer) {
              height: 2px;
              background-color: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }
  }

  .login-panel {
    .login-prompt {
      text-align: center;
      padding: 20px 12px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--text-primary);
      }
      p {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 16px;
      }
      .el-button {
        padding: 10px 20px;
        font-size: 14px;
        border-radius: 20px;
      }
    }

    .feature-list {
      .panel-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
        color: var(--text-primary);
      }

      .features {
        list-style: none;
        padding: 0;
        margin: 0;

        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .el-icon {
            font-size: 20px;
            margin-right: 12px;
            color: var(--primary-color);
          }

          span {
            font-size: 16px;
            color: var(--text-primary);
          }
        }
      }
    }

    .popular-courses {
      .panel-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
        color: var(--text-primary);
      }

      .popular-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;

        .popular-item {
          cursor: pointer;
          border-radius: 12px;
          overflow: hidden;
          border: 1px solid var(--border-color);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
          }

          .popular-image {
            width: 100%;
            height: 100px;
            object-fit: cover;
          }

          .popular-info {
            padding: 12px;

            .popular-title {
              font-size: 14px;
              font-weight: 500;
              color: var(--text-primary);
              margin-bottom: 4px;
            }
            .popular-price {
              font-size: 16px;
              font-weight: 600;
              color: var(--primary-color);
            }
          }
        }
      }
    }
  }

  .categories-section {
    padding: 80px 0;

    .section-title {
      text-align: center;
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 48px;
      color: var(--text-primary);
    }

    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;

      .category-item {
        background: var(--background-color);
        border-radius: 16px;
        padding: 32px 24px;
        text-align: center;
        border: 1px solid var(--border-color);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 12px 40px rgba(255, 107, 53, 0.15);
          border-color: var(--primary-color);
        }

        .category-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;
          color: white;
          font-size: 28px;
        }

        .category-title {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
          color: var(--text-primary);
        }

        .category-desc {
          font-size: 14px;
          color: var(--text-secondary);
          line-height: 1.5;
          margin-bottom: 12px;
        }

        .category-stats {
          display: flex;
          justify-content: center;
          gap: 16px;
          font-size: 12px;
          color: var(--text-light);
        }
      }
    }
  }

  .courses-section {
    padding: 80px 0;
    background: var(--background-light);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 48px;

      .section-title {
        font-size: 32px;
        font-weight: 600;
        color: var(--text-primary);
      }

      .section-tabs {
        display: flex;
        gap: 24px;

        .tab-item {
          padding: 8px 16px;
          font-size: 16px;
          color: var(--text-secondary);
          cursor: pointer;
          border-radius: 20px;
          transition: all 0.3s ease;

          &:hover,
          &.active {
            color: var(--primary-color);
            background: rgba(255, 107, 53, 0.1);
          }
        }
      }
    }

    .courses-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 24px;

      .course-card {
        background: var(--background-color);
        border-radius: 16px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .course-image {
          position: relative;
          height: 180px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .course-badge {
            position: absolute;
            top: 12px;
            left: 12px;
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
          }
        }

        .course-content {
          padding: 20px;

          .course-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
            line-height: 1.4;
          }

          .course-desc {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 16px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .course-instructor {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;

            .instructor-avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              object-fit: cover;
            }

            .instructor-name {
              font-size: 14px;
              color: var(--text-secondary);
            }
          }

          .course-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .course-price {
              .current-price {
                font-size: 20px;
                font-weight: 600;
                color: var(--primary-color);
                margin-right: 8px;
              }

              .original-price {
                font-size: 14px;
                color: var(--text-light);
                text-decoration: line-through;
              }
            }

            .course-stats {
              .students-count {
                font-size: 12px;
                color: var(--text-light);
              }
            }
          }
        }
      }
    }
  }

  .instructors-section {
    padding: 80px 0;

    .section-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 48px;
      color: var(--text-primary);
    }

    .instructors-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;

      .instructor-card {
        background: var(--background-color);
        border-radius: 16px;
        padding: 24px;
        text-align: center;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
        }

        .instructor-avatar {
          margin-bottom: 16px;

          img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
          }
        }

        .instructor-info {
          .instructor-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
            color: var(--text-primary);
          }

          .instructor-title {
            font-size: 14px;
            color: var(--primary-color);
            margin-bottom: 12px;
            font-weight: 500;
          }

          .instructor-desc {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.5;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .instructor-stats {
            display: flex;
            justify-content: center;
            gap: 16px;

            .stat-item {
              font-size: 12px;
              color: var(--text-light);
            }
          }
        }
      }
    }
  }

  // 响应式设计

  @media (max-width: 1400px) {
    .home-view {
      .container {
        max-width: 1600px !important;
      }
    }
  }

  @media (max-width: 1200px) {
    .home-view {
      .container {
        max-width: 1200px !important;
      }
    }
  }

  @media (max-width: 1024px) {
    .home-view {
      .categories-section,
      .courses-section,
      .instructors-section {
        padding: 40px 0;

        .section-title {
          font-size: 24px;
          margin-bottom: 32px;
        }
      }

      .courses-section {
        .section-header {
          flex-direction: column;
          gap: 16px;

          .section-tabs {
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .home-view {
      .banner-item {
        .banner-content {
          flex-direction: column;
          text-align: center;
          padding: 30px 16px;

          .banner-text {
            .banner-title {
              font-size: 28px;
              margin-bottom: 12px;
            }

            .banner-subtitle {
              font-size: 14px;
              margin-bottom: 16px;
            }

            .banner-price {
              margin-bottom: 24px;

              .current-price {
                font-size: 24px;
              }

              .original-price {
                font-size: 14px;
              }
            }

            .banner-btn {
              padding: 12px 24px;
              font-size: 14px;
            }
          }

          .banner-image {
            margin-left: 0;
            margin-top: 16px;

            img {
              width: 180px;
              height: 120px;
            }
          }
        }
      }
    }

    .categories-section,
    .courses-section,
    .instructors-section {
      padding: 32px 0;

      .section-title {
        font-size: 20px;
        margin-bottom: 24px;
      }
    }

    .courses-section {
      .section-header {
        flex-direction: column;
        gap: 12px;

        .section-tabs {
          flex-wrap: wrap;
          justify-content: center;
          gap: 8px;

          .tab-item {
            padding: 6px 12px;
            font-size: 14px;
          }
        }
      }

      .courses-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 16px;
      }
    }

    .categories-section {
      .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 16px;
      }
    }

    .instructors-section {
      .instructors-grid {
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 16px;
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .home-view {
    .banner-item {
      .banner-content {
        padding: 20px 12px;

        .banner-text {
          .banner-title {
            font-size: 24px;
          }

          .banner-subtitle {
            font-size: 12px;
          }
        }
      }
    }
  }

  .categories-section,
  .courses-section,
  .instructors-section {
    padding: 24px 0;

    .section-title {
      font-size: 18px;
      margin-bottom: 20px;
    }
  }

  .courses-section {
    .courses-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .categories-section {
    .categories-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .instructors-section {
    .instructors-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }
}
</style>
