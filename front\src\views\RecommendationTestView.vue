<template>
  <div class="recommendation-test">
    <div class="container">
      <h1>推荐系统测试</h1>
      
      <!-- 控制面板 -->
      <div class="control-panel">
        <el-card>
          <h3>测试控制</h3>
          <div class="controls">
            <el-select v-model="selectedCourseId" placeholder="选择基准课程">
              <el-option
                v-for="course in courses"
                :key="course.id"
                :label="course.title"
                :value="course.id"
              />
            </el-select>
            <el-button @click="testAllRecommendations" type="primary">
              测试所有推荐算法
            </el-button>
            <el-button @click="clearCache" type="warning">
              清除缓存
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 基于标签的推荐 -->
      <section class="test-section" v-if="tagsBasedRecommendations.length > 0">
        <h2>基于技能标签的推荐</h2>
        <p class="algorithm-desc">
          基于课程技能标签的匹配度进行推荐，匹配度越高推荐优先级越高
        </p>
        <div class="recommendations-grid">
          <div 
            v-for="rec in tagsBasedRecommendations" 
            :key="rec.course.id"
            class="recommendation-card"
          >
            <CourseCard 
              :course="rec.course"
              :show-recommendation-reason="true"
              :recommendation-text="rec.reason"
              size="compact"
            />
            <div class="recommendation-meta">
              <span class="score">匹配度: {{ rec.score.toFixed(1) }}%</span>
              <span class="type">{{ rec.type }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门趋势推荐 -->
      <section class="test-section" v-if="trendingCourses.length > 0">
        <h2>热门趋势课程</h2>
        <p class="algorithm-desc">
          基于学员数量和评分的综合热度排序
        </p>
        <div class="recommendations-grid">
          <CourseCard 
            v-for="course in trendingCourses" 
            :key="course.id"
            :course="course"
            :show-recommendation-reason="true"
            :recommendation-text="getTrendingReason(course)"
            size="compact"
          />
        </div>
      </section>

      <!-- 综合热度推荐 -->
      <section class="test-section" v-if="popularCourses.length > 0">
        <h2>综合热度推荐</h2>
        <p class="algorithm-desc">
          综合评分、学员数和热门标签的推荐算法
        </p>
        <div class="recommendations-grid">
          <CourseCard 
            v-for="course in popularCourses" 
            :key="course.id"
            :course="course"
            :show-recommendation-reason="true"
            :recommendation-text="getPopularReason(course)"
            size="compact"
          />
        </div>
      </section>

      <!-- 相似难度推荐 -->
      <section class="test-section" v-if="similarLevelRecommendations.length > 0">
        <h2>相似难度推荐</h2>
        <p class="algorithm-desc">
          基于课程难度级别的推荐，包含相同级别和进阶级别
        </p>
        <div class="recommendations-grid">
          <div 
            v-for="rec in similarLevelRecommendations" 
            :key="rec.course.id"
            class="recommendation-card"
          >
            <CourseCard 
              :course="rec.course"
              :show-recommendation-reason="true"
              :recommendation-text="rec.reason"
              size="compact"
            />
            <div class="recommendation-meta">
              <span class="score">推荐度: {{ rec.score }}%</span>
              <span class="level">{{ rec.course.difficulty }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 协同过滤推荐 -->
      <section class="test-section" v-if="collaborativeRecommendations.length > 0">
        <h2>协同过滤推荐</h2>
        <p class="algorithm-desc">
          基于相同分类和相似评分的协同过滤算法
        </p>
        <div class="recommendations-grid">
          <div 
            v-for="rec in collaborativeRecommendations" 
            :key="rec.course.id"
            class="recommendation-card"
          >
            <CourseCard 
              :course="rec.course"
              :show-recommendation-reason="true"
              :recommendation-text="rec.reason"
              size="compact"
            />
            <div class="recommendation-meta">
              <span class="score">相似度: {{ rec.score.toFixed(1) }}%</span>
              <span class="category">{{ rec.course.category }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 缓存状态 -->
      <div class="cache-status">
        <el-card>
          <h3>缓存状态</h3>
          <p>缓存项数量: {{ cacheSize }}</p>
          <p>是否有推荐数据: {{ hasRecommendations ? '是' : '否' }}</p>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRecommendationStore } from '@/stores/recommendation'
import CourseCard from '@/components/common/CourseCard.vue'
import { courses } from '@/data/mockData'
import type { RecommendationResult } from '@/stores/recommendation'

const recommendationStore = useRecommendationStore()

// 响应式数据
const selectedCourseId = ref('1') // 默认选择第一个课程
const tagsBasedRecommendations = ref<RecommendationResult[]>([])
const similarLevelRecommendations = ref<RecommendationResult[]>([])
const collaborativeRecommendations = ref<RecommendationResult[]>([])

// 计算属性
const trendingCourses = computed(() => recommendationStore.trendingCourses)
const popularCourses = computed(() => recommendationStore.popularCourses)
const hasRecommendations = computed(() => recommendationStore.hasRecommendations)
const cacheSize = computed(() => recommendationStore.cacheSize)

// 测试所有推荐算法
const testAllRecommendations = () => {
  if (!selectedCourseId.value) return

  // 测试基于标签的推荐
  tagsBasedRecommendations.value = recommendationStore.getRecommendationsByTags(
    selectedCourseId.value, 
    4
  )

  // 测试热门趋势推荐
  recommendationStore.getTrendingCourses(6)

  // 测试综合热度推荐
  recommendationStore.getPopularCourses(8)

  // 测试相似难度推荐
  similarLevelRecommendations.value = recommendationStore.getSimilarLevelCourses(
    selectedCourseId.value, 
    4
  )

  // 测试协同过滤推荐
  collaborativeRecommendations.value = recommendationStore.getCollaborativeRecommendations(
    selectedCourseId.value, 
    4
  )
}

// 清除缓存
const clearCache = () => {
  recommendationStore.clearAllCache()
  // 清除本地数据
  tagsBasedRecommendations.value = []
  similarLevelRecommendations.value = []
  collaborativeRecommendations.value = []
}

// 获取热门推荐理由（模拟）
const getTrendingReason = (course: any) => {
  return `${course.studentsCount}人正在学习，本周热度上升`
}

// 获取热度推荐理由（模拟）
const getPopularReason = (course: any) => {
  return `${course.rating}分高评价，综合热度推荐`
}

// 初始化测试
testAllRecommendations()
</script>

<style lang="scss" scoped>
.recommendation-test {
  padding: 40px 0;
  min-height: 100vh;
  background: var(--bg-secondary);

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
  }

  h1 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 40px;
  }

  .control-panel {
    margin-bottom: 40px;

    h3 {
      color: var(--text-primary);
      margin-bottom: 16px;
    }

    .controls {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }
  }

  .test-section {
    margin-bottom: 60px;

    h2 {
      color: var(--text-primary);
      margin-bottom: 8px;
    }

    .algorithm-desc {
      color: var(--text-secondary);
      font-size: 14px;
      margin-bottom: 24px;
      padding: 12px;
      background: var(--bg-tertiary);
      border-radius: 8px;
      border-left: 4px solid var(--brand-primary);
    }

    .recommendations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
    }

    .recommendation-card {
      position: relative;

      .recommendation-meta {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: var(--text-tertiary);

        .score {
          color: var(--brand-primary);
          font-weight: 500;
        }

        .type, .level, .category {
          background: var(--bg-tertiary);
          padding: 2px 8px;
          border-radius: 4px;
        }
      }
    }
  }

  .cache-status {
    margin-top: 40px;

    h3 {
      color: var(--text-primary);
      margin-bottom: 16px;
    }

    p {
      color: var(--text-secondary);
      margin: 8px 0;
    }
  }
}
</style>
