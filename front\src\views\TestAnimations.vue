<template>
  <div class="test-animations">
    <div class="container">
      <h1 class="fade-in-down">动画效果测试页面</h1>
      
      <!-- 基础动画展示 -->
      <section class="animation-showcase">
        <h2 class="fade-in-up delay-100">基础动画效果</h2>
        
        <div class="animation-grid">
          <div class="animation-item fade-in-up delay-200">
            <h3>淡入向上</h3>
            <div class="demo-box fade-in-up">fade-in-up</div>
          </div>
          
          <div class="animation-item fade-in-down delay-300">
            <h3>淡入向下</h3>
            <div class="demo-box fade-in-down">fade-in-down</div>
          </div>
          
          <div class="animation-item fade-in-left delay-400">
            <h3>淡入向左</h3>
            <div class="demo-box fade-in-left">fade-in-left</div>
          </div>
          
          <div class="animation-item fade-in-right delay-500">
            <h3>淡入向右</h3>
            <div class="demo-box fade-in-right">fade-in-right</div>
          </div>
        </div>
      </section>

      <!-- 悬停效果展示 -->
      <section class="hover-showcase scroll-reveal">
        <h2>悬停效果展示</h2>
        
        <div class="hover-grid">
          <div class="hover-item">
            <h3>卡片悬停</h3>
            <div class="demo-card hover-lift">
              <p>悬停我试试</p>
            </div>
          </div>
          
          <div class="hover-item">
            <h3>按钮悬停</h3>
            <button class="demo-button hover-scale">点击按钮</button>
          </div>
          
          <div class="hover-item">
            <h3>图片悬停</h3>
            <div class="demo-image hover-zoom">
              <img src="https://picsum.photos/200/150" alt="测试图片" />
            </div>
          </div>
          
          <div class="hover-item">
            <h3>文字悬停</h3>
            <a href="#" class="demo-link hover-underline">悬停下划线</a>
          </div>
        </div>
      </section>

      <!-- 数字动画展示 -->
      <section class="number-showcase scroll-reveal">
        <h2>数字滚动动画</h2>
        
        <div class="number-grid">
          <div class="number-item">
            <div class="number-display">{{ animatedNumber1 }}</div>
            <div class="number-label">总课程数</div>
          </div>
          
          <div class="number-item">
            <div class="number-display">{{ animatedNumber2 }}</div>
            <div class="number-label">学习人数</div>
          </div>
          
          <div class="number-item">
            <div class="number-display">{{ animatedNumber3 }}</div>
            <div class="number-label">学习时长</div>
          </div>
        </div>
        
        <el-button @click="startNumberAnimation" type="primary">重新播放数字动画</el-button>
      </section>

      <!-- 特殊效果展示 -->
      <section class="special-showcase scroll-reveal">
        <h2>特殊效果展示</h2>
        
        <div class="special-grid">
          <div class="special-item">
            <h3>脉冲动画</h3>
            <div class="demo-box pulse">pulse</div>
          </div>
          
          <div class="special-item">
            <h3>摇摆动画</h3>
            <div class="demo-box shake">shake</div>
          </div>
          
          <div class="special-item">
            <h3>弹跳动画</h3>
            <div class="demo-box bounce">bounce</div>
          </div>
          
          <div class="special-item">
            <h3>渐变背景</h3>
            <div class="demo-box gradient-animation">gradient</div>
          </div>
        </div>
      </section>

      <!-- 加载动画展示 -->
      <section class="loading-showcase scroll-reveal">
        <h2>加载动画展示</h2>
        
        <div class="loading-grid">
          <div class="loading-item">
            <h3>骨架屏</h3>
            <div class="skeleton-demo">
              <div class="skeleton" style="height: 20px; margin-bottom: 10px;"></div>
              <div class="skeleton" style="height: 20px; width: 80%; margin-bottom: 10px;"></div>
              <div class="skeleton" style="height: 20px; width: 60%;"></div>
            </div>
          </div>
          
          <div class="loading-item">
            <h3>旋转加载</h3>
            <div class="spin-demo">
              <el-icon class="spin"><Loading /></el-icon>
            </div>
          </div>
        </div>
      </section>

      <!-- 滚动动画展示 -->
      <section class="scroll-showcase">
        <h2>滚动触发动画</h2>
        <p>向下滚动查看更多动画效果</p>
        
        <div class="scroll-items">
          <div v-for="i in 6" :key="i" class="scroll-item scroll-reveal">
            <h3>滚动项目 {{ i }}</h3>
            <p>这个元素会在滚动到视口时触发动画</p>
          </div>
        </div>
      </section>

      <!-- 控制面板 -->
      <section class="control-panel">
        <h2>动画控制</h2>
        
        <div class="controls">
          <el-button @click="triggerAllAnimations" type="primary">
            重新播放所有动画
          </el-button>
          
          <el-button @click="toggleReducedMotion" type="default">
            {{ reducedMotion ? '启用' : '禁用' }}动画
          </el-button>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { useCountUp, useScrollAnimation } from '@/composables/useIntersectionObserver'

// 数字动画
const { current: animatedNumber1, start: startNumber1 } = useCountUp(156, 2000)
const { current: animatedNumber2, start: startNumber2 } = useCountUp(12580, 2500)
const { current: animatedNumber3, start: startNumber3 } = useCountUp(89760, 3000)

const reducedMotion = ref(false)

// 开始数字动画
const startNumberAnimation = () => {
  startNumber1()
  setTimeout(() => startNumber2(), 200)
  setTimeout(() => startNumber3(), 400)
}

// 重新播放所有动画
const triggerAllAnimations = () => {
  // 移除所有动画类
  const elements = document.querySelectorAll('.fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right, .revealed')
  elements.forEach(el => {
    el.classList.remove('fade-in-up', 'fade-in-down', 'fade-in-left', 'fade-in-right', 'revealed')
  })
  
  // 重新添加动画类
  setTimeout(() => {
    elements.forEach((el, index) => {
      setTimeout(() => {
        if (el.classList.contains('scroll-reveal')) {
          el.classList.add('revealed')
        } else {
          el.classList.add('fade-in-up')
        }
      }, index * 100)
    })
  }, 100)
  
  // 重新播放数字动画
  startNumberAnimation()
}

// 切换减少动画模式
const toggleReducedMotion = () => {
  reducedMotion.value = !reducedMotion.value
  
  if (reducedMotion.value) {
    document.body.style.setProperty('--animation-duration', '0.01ms')
  } else {
    document.body.style.removeProperty('--animation-duration')
  }
}

// 组件挂载时开始动画
onMounted(() => {
  startNumberAnimation()
  
  // 设置滚动动画
  const scrollElements = document.querySelectorAll('.scroll-reveal')
  scrollElements.forEach(el => {
    useScrollAnimation(el as HTMLElement, 'fade-in-up', {
      threshold: 0.1,
      once: false // 允许重复触发
    })
  })
})
</script>

<style scoped lang="scss">
.test-animations {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 40px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 40px;
      font-size: 36px;
    }
  }

  section {
    margin-bottom: 60px;
    padding: 40px;
    background: var(--bg-secondary);
    border-radius: 16px;
    border: 1px solid var(--border-primary);

    h2 {
      color: var(--text-primary);
      margin-bottom: 30px;
      font-size: 24px;
      text-align: center;
    }
  }

  .animation-grid,
  .hover-grid,
  .number-grid,
  .special-grid,
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }

  .animation-item,
  .hover-item,
  .number-item,
  .special-item,
  .loading-item {
    text-align: center;
    padding: 20px;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-primary);

    h3 {
      color: var(--text-primary);
      margin-bottom: 15px;
      font-size: 16px;
    }
  }

  .demo-box {
    width: 120px;
    height: 80px;
    background: var(--brand-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin: 0 auto;
    font-weight: 500;
  }

  .demo-card {
    padding: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    cursor: pointer;

    p {
      margin: 0;
      color: var(--text-secondary);
    }
  }

  .demo-button {
    padding: 12px 24px;
    background: var(--brand-primary);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
  }

  .demo-image {
    width: 200px;
    height: 150px;
    margin: 0 auto;
    border-radius: 8px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .demo-link {
    color: var(--brand-primary);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
  }

  .number-display {
    font-size: 48px;
    font-weight: 700;
    color: var(--brand-primary);
    margin-bottom: 8px;
  }

  .number-label {
    color: var(--text-secondary);
    font-size: 14px;
  }

  .skeleton-demo {
    padding: 20px;
    background: var(--bg-primary);
    border-radius: 8px;
  }

  .spin-demo {
    font-size: 32px;
    color: var(--brand-primary);
  }

  .scroll-items {
    display: flex;
    flex-direction: column;
    gap: 40px;
    margin-top: 40px;
  }

  .scroll-item {
    padding: 30px;
    background: var(--bg-primary);
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    text-align: center;

    h3 {
      color: var(--text-primary);
      margin-bottom: 10px;
    }

    p {
      color: var(--text-secondary);
      margin: 0;
    }
  }

  .control-panel {
    text-align: center;

    .controls {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-animations {
    padding: 20px 0;

    .container {
      padding: 0 16px;

      h1 {
        font-size: 28px;
      }
    }

    section {
      padding: 20px;
      margin-bottom: 30px;

      h2 {
        font-size: 20px;
      }
    }

    .animation-grid,
    .hover-grid,
    .number-grid,
    .special-grid,
    .loading-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .number-display {
      font-size: 36px;
    }
  }
}
</style>
