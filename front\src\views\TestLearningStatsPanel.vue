<template>
  <div class="test-page">
    <div class="container">
      <h1>学习统计面板测试页面</h1>
      
      <div class="test-layout">
        <!-- 左侧：LearningStatsPanel组件 -->
        <div class="stats-container">
          <h2>LearningStatsPanel 组件</h2>
          <LearningStatsPanel />
        </div>
        
        <!-- 右侧：功能说明 -->
        <div class="info-container">
          <h2>组件功能说明</h2>
          <div class="feature-list">
            <div class="feature-item">
              <h3>📊 学习统计</h3>
              <p>显示精品课程数量、学员总数、总学习时长等核心数据，支持数字滚动动画效果</p>
            </div>
            
            <div class="feature-item">
              <h3>⚡ 实时动态</h3>
              <p>展示当前在线用户数和今日新增用户数，带有呼吸灯效果的实时指示器</p>
            </div>
            
            <div class="feature-item">
              <h3>🔥 热门趋势</h3>
              <p>基于学员数量排序的热门课程列表，显示增长率和学习人数</p>
            </div>
            
            <div class="feature-item">
              <h3>⭐ 技能热度</h3>
              <p>根据课程技能标签统计的热门技能，热度越高透明度越高</p>
            </div>
          </div>
          
          <div class="data-source">
            <h3>数据来源</h3>
            <ul>
              <li>课程数据：从 mockData.ts 中的 courses 数组计算</li>
              <li>学员统计：汇总所有课程的 studentsCount</li>
              <li>学习时长：基于课程数和学员数的估算</li>
              <li>实时数据：模拟数据，每30秒更新一次</li>
              <li>技能热度：基于课程 skills 标签的统计分析</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LearningStatsPanel from '@/components/home/<USER>'
</script>

<style scoped lang="scss">
.test-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 20px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 30px;
      font-size: 28px;
    }
  }

  .test-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    align-items: start;

    .stats-container {
      h2 {
        color: var(--text-primary);
        margin-bottom: 16px;
        font-size: 18px;
      }
    }

    .info-container {
      background: var(--bg-primary);
      padding: 24px;
      border-radius: 12px;
      border: 1px solid var(--border-primary);

      h2 {
        color: var(--text-primary);
        margin-bottom: 20px;
        font-size: 18px;
      }

      .feature-list {
        margin-bottom: 24px;

        .feature-item {
          margin-bottom: 16px;
          padding: 16px;
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-primary);

          h3 {
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          p {
            color: var(--text-secondary);
            font-size: 13px;
            line-height: 1.5;
            margin: 0;
          }
        }
      }

      .data-source {
        h3 {
          color: var(--text-primary);
          margin-bottom: 12px;
          font-size: 14px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.6;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    .test-layout {
      grid-template-columns: 1fr;
      gap: 20px;

      .stats-container {
        order: 2;
      }

      .info-container {
        order: 1;
        padding: 16px;
      }
    }
  }
}
</style>
