<template>
  <div class="test-page">
    <div class="container">
      <h1>个人学习仪表板组件测试页面</h1>
      
      <div class="test-layout">
        <!-- 个人仪表板组件 -->
        <div class="dashboard-container">
          <h2>PersonalDashboard 组件</h2>
          <div class="dashboard-wrapper">
            <PersonalDashboard />
          </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="info-container">
          <h2>组件功能说明</h2>
          <div class="feature-list">
            <div class="feature-item">
              <h3>👤 用户状态管理</h3>
              <p>自动检测用户登录状态，显示不同的界面内容</p>
            </div>
            
            <div class="feature-item">
              <h3>📊 学习进度展示</h3>
              <p>显示今日学习目标、学习时长、完成课时等进度信息</p>
            </div>
            
            <div class="feature-item">
              <h3>🎯 继续学习功能</h3>
              <p>展示最近学习的课程，支持一键继续学习</p>
            </div>
            
            <div class="feature-item">
              <h3>🏆 学习成就系统</h3>
              <p>显示完成课程数、获得证书、学习小时等成就数据</p>
            </div>
            
            <div class="feature-item">
              <h3>🚀 快速操作</h3>
              <p>提供我的课程、学习计划等快速入口</p>
            </div>
          </div>
          
          <div class="state-info">
            <h3>状态说明</h3>
            <ul>
              <li><strong>已登录状态：</strong>显示完整的学习仪表板</li>
              <li><strong>未登录状态：</strong>显示登录引导和热门学习路径</li>
              <li><strong>用户等级：</strong>根据完成课程数自动计算</li>
              <li><strong>学习目标：</strong>支持时长和课时两种目标类型</li>
            </ul>
          </div>
          
          <div class="test-controls">
            <h3>测试控制</h3>
            <div class="control-buttons">
              <el-button 
                type="primary" 
                @click="toggleLoginState"
                size="small"
              >
                {{ userStore.isLoggedIn ? '退出登录' : '模拟登录' }}
              </el-button>
              <el-button 
                type="default" 
                @click="updateProgress"
                size="small"
                :disabled="!userStore.isLoggedIn"
              >
                更新学习进度
              </el-button>
            </div>
          </div>
          
          <div class="data-source">
            <h3>数据来源</h3>
            <ul>
              <li>用户数据：从 useUserStore 状态管理获取</li>
              <li>学习进度：基于用户的 learningProgress 数据</li>
              <li>最近学习：从 user.recentLearning 数组获取</li>
              <li>学习目标：模拟的每日学习目标数据</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PersonalDashboard from '@/components/home/<USER>'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 切换登录状态
const toggleLoginState = () => {
  if (userStore.isLoggedIn) {
    userStore.logout()
  } else {
    userStore.login('demo', '123456')
  }
}

// 更新学习进度（模拟）
const updateProgress = () => {
  if (userStore.user) {
    // 模拟增加今日学习时长
    userStore.user.learningProgress.todayStudyTime += 15
    
    // 模拟增加总学习时长
    userStore.user.learningProgress.totalStudyTime += 15
    
    // 可能增加连续学习天数
    if (Math.random() > 0.7) {
      userStore.user.learningProgress.consecutiveDays += 1
    }
  }
}
</script>

<style scoped lang="scss">
.test-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 20px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 30px;
      font-size: 28px;
    }
  }

  .test-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 30px;
    align-items: start;

    .dashboard-container {
      h2 {
        color: var(--text-primary);
        margin-bottom: 16px;
        font-size: 18px;
      }

      .dashboard-wrapper {
        height: 600px;
        border: 2px dashed var(--border-primary);
        border-radius: 12px;
        padding: 4px;
        background: var(--bg-primary);
      }
    }

    .info-container {
      background: var(--bg-primary);
      padding: 24px;
      border-radius: 12px;
      border: 1px solid var(--border-primary);

      h2 {
        color: var(--text-primary);
        margin-bottom: 20px;
        font-size: 18px;
      }

      .feature-list {
        margin-bottom: 24px;

        .feature-item {
          margin-bottom: 16px;
          padding: 16px;
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-primary);

          h3 {
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          p {
            color: var(--text-secondary);
            font-size: 13px;
            line-height: 1.5;
            margin: 0;
          }
        }
      }

      .state-info,
      .test-controls,
      .data-source {
        margin-bottom: 20px;

        h3 {
          color: var(--text-primary);
          margin-bottom: 12px;
          font-size: 14px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.6;
            margin-bottom: 4px;

            strong {
              color: var(--text-primary);
            }
          }
        }
      }

      .test-controls {
        .control-buttons {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    .test-layout {
      grid-template-columns: 1fr;
      gap: 20px;

      .dashboard-container {
        order: 2;

        .dashboard-wrapper {
          height: 500px;
        }
      }

      .info-container {
        order: 1;
        padding: 16px;
      }
    }
  }
}
</style>
