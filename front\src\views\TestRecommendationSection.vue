<template>
  <div class="test-page">
    <div class="container">
      <h1>智能推荐课程区域组件测试页面</h1>
      
      <div class="test-layout">
        <!-- 推荐区域组件 -->
        <div class="component-container">
          <h2>RecommendationSection 组件</h2>
          <div class="component-wrapper">
            <RecommendationSection />
          </div>
        </div>
        
        <!-- 功能说明 -->
        <div class="info-container">
          <h2>组件功能说明</h2>
          <div class="feature-list">
            <div class="feature-item">
              <h3>🤝 协同推荐</h3>
              <p>基于"学习相同课程的用户还在学什么"的协同过滤算法</p>
              <ul>
                <li>分析用户学习行为相似性</li>
                <li>推荐理由：学习Vue3的同学92%都选择了这门进阶课</li>
                <li>社交证明增强用户信任度</li>
              </ul>
            </div>
            
            <div class="feature-item">
              <h3>🔥 热门趋势</h3>
              <p>展示当前最受欢迎、学习人数增长最快的课程</p>
              <ul>
                <li>基于7天内学习人数增长率</li>
                <li>推荐理由：本周学习人数增长150%</li>
                <li>实时热度数据更新</li>
              </ul>
            </div>
            
            <div class="feature-item">
              <h3>🚀 技能进阶</h3>
              <p>根据用户技能栈匹配相关进阶课程</p>
              <ul>
                <li>基于技能标签智能匹配</li>
                <li>推荐理由：完美匹配您的JavaScript技能栈</li>
                <li>学习路径连贯性保证</li>
              </ul>
            </div>
            
            <div class="feature-item">
              <h3>⭐ 综合热度</h3>
              <p>综合评分、完课率、学习人数等多维度推荐</p>
              <ul>
                <li>多因子加权算法</li>
                <li>推荐理由：综合评分4.8分，85%完课率</li>
                <li>质量保证的精选课程</li>
              </ul>
            </div>
          </div>
          
          <div class="technical-features">
            <h3>技术特性</h3>
            <ul>
              <li><strong>智能标签切换：</strong>4种推荐算法无缝切换</li>
              <li><strong>动态推荐理由：</strong>每门课程生成个性化推荐文案</li>
              <li><strong>加载状态管理：</strong>优雅的loading和空状态处理</li>
              <li><strong>分页加载：</strong>初始8门课程，支持查看更多</li>
              <li><strong>URL状态同步：</strong>推荐类型保存在URL参数中</li>
              <li><strong>响应式设计：</strong>完美适配各种屏幕尺寸</li>
            </ul>
          </div>
          
          <div class="data-flow">
            <h3>数据流说明</h3>
            <div class="flow-steps">
              <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                  <h4>推荐算法选择</h4>
                  <p>用户点击标签 → 切换推荐算法类型</p>
                </div>
              </div>
              <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                  <h4>数据获取</h4>
                  <p>调用useRecommendationStore对应方法获取课程数据</p>
                </div>
              </div>
              <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                  <h4>推荐理由生成</h4>
                  <p>为每门课程生成个性化推荐文案</p>
                </div>
              </div>
              <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                  <h4>CourseCard渲染</h4>
                  <p>使用CourseCard组件展示课程信息和推荐理由</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="test-controls">
            <h3>测试功能</h3>
            <div class="control-buttons">
              <el-button 
                type="primary" 
                @click="testRecommendationRefresh"
                size="small"
              >
                刷新推荐数据
              </el-button>
              <el-button 
                type="default" 
                @click="testUrlParams"
                size="small"
              >
                测试URL参数
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import RecommendationSection from '@/components/home/<USER>'
import { useRecommendationStore } from '@/stores/recommendation'

const recommendationStore = useRecommendationStore()

// 测试推荐数据刷新
const testRecommendationRefresh = async () => {
  await recommendationStore.refreshRecommendations()
  console.log('推荐数据已刷新')
}

// 测试URL参数功能
const testUrlParams = () => {
  const params = ['collaborative', 'trending', 'skill-based', 'popular']
  const randomParam = params[Math.floor(Math.random() * params.length)]
  
  const url = new URL(window.location.href)
  url.searchParams.set('recommend', randomParam)
  window.location.href = url.toString()
}
</script>

<style scoped lang="scss">
.test-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 20px 0;

  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 30px;
      font-size: 28px;
    }
  }

  .test-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    align-items: start;

    .component-container {
      h2 {
        color: var(--text-primary);
        margin-bottom: 16px;
        font-size: 18px;
      }

      .component-wrapper {
        border: 2px dashed var(--border-primary);
        border-radius: 12px;
        padding: 4px;
        background: var(--bg-primary);
      }
    }

    .info-container {
      background: var(--bg-primary);
      padding: 24px;
      border-radius: 12px;
      border: 1px solid var(--border-primary);
      max-height: 80vh;
      overflow-y: auto;

      h2 {
        color: var(--text-primary);
        margin-bottom: 20px;
        font-size: 18px;
      }

      .feature-list {
        margin-bottom: 24px;

        .feature-item {
          margin-bottom: 20px;
          padding: 16px;
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-primary);

          h3 {
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          p {
            color: var(--text-secondary);
            font-size: 13px;
            line-height: 1.5;
            margin: 0 0 8px 0;
          }

          ul {
            margin: 0;
            padding-left: 16px;

            li {
              color: var(--text-tertiary);
              font-size: 12px;
              line-height: 1.4;
              margin-bottom: 4px;
            }
          }
        }
      }

      .technical-features,
      .data-flow,
      .test-controls {
        margin-bottom: 20px;

        h3 {
          color: var(--text-primary);
          margin-bottom: 12px;
          font-size: 14px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.6;
            margin-bottom: 4px;

            strong {
              color: var(--text-primary);
            }
          }
        }
      }

      .data-flow {
        .flow-steps {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .step {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px;
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-primary);

            .step-number {
              width: 24px;
              height: 24px;
              background: var(--brand-primary);
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 600;
              flex-shrink: 0;
            }

            .step-content {
              flex: 1;

              h4 {
                color: var(--text-primary);
                font-size: 13px;
                margin: 0 0 4px 0;
              }

              p {
                color: var(--text-secondary);
                font-size: 12px;
                margin: 0;
                line-height: 1.4;
              }
            }
          }
        }
      }

      .test-controls {
        .control-buttons {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .test-page {
    .test-layout {
      grid-template-columns: 1fr;
      gap: 20px;

      .component-container {
        order: 1;
      }

      .info-container {
        order: 2;
        max-height: none;
        padding: 16px;
      }
    }
  }
}
</style>
