<template>
  <div class="test-page">
    <div class="container">
      <h1>智能推荐轮播组件测试页面</h1>
      
      <div class="test-layout">
        <!-- 智能推荐轮播组件 -->
        <div class="carousel-container">
          <h2>SmartRecommendationCarousel 组件</h2>
          <SmartRecommendationCarousel />
        </div>
        
        <!-- 功能说明 -->
        <div class="info-container">
          <h2>组件功能说明</h2>
          <div class="feature-list">
            <div class="feature-item">
              <h3>🎯 智能推荐算法</h3>
              <p>支持5种推荐算法：热门趋势、兴趣匹配、综合热度、难度适配、协同推荐</p>
            </div>
            
            <div class="feature-item">
              <h3>👥 社交证明</h3>
              <p>显示学习者头像、在线学习人数、课程完成率等社交证明信息</p>
            </div>
            
            <div class="feature-item">
              <h3>🎨 视觉吸引力</h3>
              <p>渐变背景、卡片式设计、悬停动画、播放按钮等视觉元素</p>
            </div>
            
            <div class="feature-item">
              <h3>📱 响应式设计</h3>
              <p>适配桌面端和移动端，自动调整布局和交互方式</p>
            </div>
            
            <div class="feature-item">
              <h3>🔄 自动轮播</h3>
              <p>6秒自动切换，支持手动控制和悬停暂停</p>
            </div>
          </div>
          
          <div class="algorithm-info">
            <h3>推荐算法说明</h3>
            <ul>
              <li><strong>🔥 热门趋势：</strong>基于学习人数和热度排序</li>
              <li><strong>🎯 兴趣匹配：</strong>根据课程技能标签匹配</li>
              <li><strong>⭐ 综合热度：</strong>综合评分、人数、评价等因素</li>
              <li><strong>📈 难度适配：</strong>根据课程难度级别推荐</li>
              <li><strong>👥 协同推荐：</strong>基于相似用户的学习行为</li>
            </ul>
          </div>
          
          <div class="data-source">
            <h3>数据来源</h3>
            <ul>
              <li>课程数据：从 mockData.ts 中的 courses 数组获取</li>
              <li>推荐逻辑：使用 useRecommendationStore 状态管理</li>
              <li>社交证明：基于真实数据的模拟计算</li>
              <li>学习者头像：Element Plus 提供的示例头像</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SmartRecommendationCarousel from '@/components/home/<USER>'
</script>

<style scoped lang="scss">
.test-page {
  min-height: 100vh;
  background: var(--bg-secondary);
  padding: 20px 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 30px;
      font-size: 28px;
    }
  }

  .test-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    align-items: start;

    .carousel-container {
      h2 {
        color: var(--text-primary);
        margin-bottom: 16px;
        font-size: 18px;
      }
    }

    .info-container {
      background: var(--bg-primary);
      padding: 24px;
      border-radius: 12px;
      border: 1px solid var(--border-primary);

      h2 {
        color: var(--text-primary);
        margin-bottom: 20px;
        font-size: 18px;
      }

      .feature-list {
        margin-bottom: 24px;

        .feature-item {
          margin-bottom: 16px;
          padding: 16px;
          background: var(--bg-secondary);
          border-radius: 8px;
          border: 1px solid var(--border-primary);

          h3 {
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          p {
            color: var(--text-secondary);
            font-size: 13px;
            line-height: 1.5;
            margin: 0;
          }
        }
      }

      .algorithm-info,
      .data-source {
        margin-bottom: 20px;

        h3 {
          color: var(--text-primary);
          margin-bottom: 12px;
          font-size: 14px;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            color: var(--text-secondary);
            font-size: 12px;
            line-height: 1.6;
            margin-bottom: 4px;

            strong {
              color: var(--text-primary);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-page {
    .test-layout {
      grid-template-columns: 1fr;
      gap: 20px;

      .carousel-container {
        order: 2;
      }

      .info-container {
        order: 1;
        padding: 16px;
      }
    }
  }
}
</style>
